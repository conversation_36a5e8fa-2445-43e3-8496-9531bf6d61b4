using System.Data.SQLite;
using System.Timers;
using Serilog;

namespace Mis.Agent.Services
{
    /// <summary>
    /// Background service for automatic data cleanup
    /// </summary>
    public class DataCleanupService : IDisposable
    {
        private readonly System.Timers.Timer _cleanupTimer;
        private readonly string _databaseFile;
        private readonly string _logsDirectory;
        private readonly object _lock = new object();
        private bool _disposed = false;

        // Cleanup intervals
        private const int CLEANUP_INTERVAL_HOURS = 24;
        private const int MAX_NOTIFICATION_RECORDS = 10000;

        public DataCleanupService()
        {
            var baseDirectory = AppContext.BaseDirectory;
            _databaseFile = Path.Combine(baseDirectory, "notifications.db");
            _logsDirectory = Path.Combine(baseDirectory, "Logs");

            // Set up timer for 24-hour cleanup cycle
            _cleanupTimer = new System.Timers.Timer(TimeSpan.FromHours(CLEANUP_INTERVAL_HOURS).TotalMilliseconds);
            _cleanupTimer.Elapsed += OnCleanupTimer;
            _cleanupTimer.AutoReset = true;
            _cleanupTimer.Enabled = true;

            // Run initial cleanup
            Task.Run(async () => await PerformCleanupAsync());
        }

        /// <summary>
        /// Timer event handler for periodic cleanup
        /// </summary>
        private async void OnCleanupTimer(object sender, ElapsedEventArgs e)
        {
            await PerformCleanupAsync();
        }

        /// <summary>
        /// Performs comprehensive data cleanup
        /// </summary>
        public async Task PerformCleanupAsync()
        {
            try
            {
                Log.Information("Starting automatic data cleanup...");

                await Task.Run(() =>
                {
                    lock (_lock)
                    {
                        CleanupNotifications();
                        CleanupLogs();
                    }
                });

                Log.Information("Automatic data cleanup completed successfully.");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error during automatic data cleanup");
            }
        }

        /// <summary>
        /// Cleans up notifications table based on time and record count
        /// </summary>
        private void CleanupNotifications()
        {
            try
            {
                if (!File.Exists(_databaseFile))
                {
                    Log.Information("Notifications database file does not exist, skipping cleanup.");
                    return;
                }

                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    connection.Open();

                    // Check total record count
                    var countQuery = "SELECT COUNT(*) FROM Notifications";
                    using (var countCommand = new SQLiteCommand(countQuery, connection))
                    {
                        var totalRecords = Convert.ToInt32(countCommand.ExecuteScalar());
                        Log.Information($"Total notification records: {totalRecords}");

                        if (totalRecords > MAX_NOTIFICATION_RECORDS)
                        {
                            // Delete oldest records, keeping only the latest MAX_NOTIFICATION_RECORDS
                            var deleteOldQuery = @"
                                DELETE FROM Notifications 
                                WHERE Id NOT IN (
                                    SELECT Id FROM Notifications 
                                    ORDER BY ReceiveTime DESC 
                                    LIMIT @maxRecords
                                )";

                            using (var deleteCommand = new SQLiteCommand(deleteOldQuery, connection))
                            {
                                deleteCommand.Parameters.AddWithValue("@maxRecords", MAX_NOTIFICATION_RECORDS);
                                var deletedCount = deleteCommand.ExecuteNonQuery();
                                Log.Information($"Deleted {deletedCount} old notification records due to count limit.");
                            }
                        }
                    }

                    // Delete records older than 24 hours
                    var deleteOldTimeQuery = @"
                        DELETE FROM Notifications 
                        WHERE ReceiveTime < datetime('now', '-24 hours')";

                    using (var deleteTimeCommand = new SQLiteCommand(deleteOldTimeQuery, connection))
                    {
                        var deletedTimeCount = deleteTimeCommand.ExecuteNonQuery();
                        if (deletedTimeCount > 0)
                        {
                            Log.Information($"Deleted {deletedTimeCount} notification records older than 24 hours.");
                        }
                    }

                    // Vacuum database to reclaim space
                    using (var vacuumCommand = new SQLiteCommand("VACUUM", connection))
                    {
                        vacuumCommand.ExecuteNonQuery();
                        Log.Information("Database vacuum completed.");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error cleaning up notifications");
            }
        }

        /// <summary>
        /// Cleans up log files older than 24 hours
        /// </summary>
        private void CleanupLogs()
        {
            try
            {
                if (!Directory.Exists(_logsDirectory))
                {
                    Log.Information("Logs directory does not exist, skipping log cleanup.");
                    return;
                }

                var cutoffTime = DateTime.Now.AddHours(-CLEANUP_INTERVAL_HOURS);
                var logFiles = Directory.GetFiles(_logsDirectory, "*.txt", SearchOption.AllDirectories);
                var deletedCount = 0;

                foreach (var logFile in logFiles)
                {
                    try
                    {
                        var fileInfo = new FileInfo(logFile);
                        if (fileInfo.LastWriteTime < cutoffTime)
                        {
                            File.Delete(logFile);
                            deletedCount++;
                            Log.Information($"Deleted old log file: {logFile}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Warning(ex, $"Failed to delete log file: {logFile}");
                    }
                }

                if (deletedCount > 0)
                {
                    Log.Information($"Deleted {deletedCount} old log files.");
                }
                else
                {
                    Log.Information("No old log files found for cleanup.");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error cleaning up log files");
            }
        }

        /// <summary>
        /// Manually triggers cleanup (for testing or immediate cleanup needs)
        /// </summary>
        public async Task TriggerManualCleanupAsync()
        {
            Log.Information("Manual cleanup triggered.");
            await PerformCleanupAsync();
        }

        /// <summary>
        /// Gets cleanup statistics
        /// </summary>
        public async Task<CleanupStats> GetCleanupStatsAsync()
        {
            var stats = new CleanupStats();

            try
            {
                // Get notification count
                if (File.Exists(_databaseFile))
                {
                    using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                    {
                        connection.Open();
                        var countQuery = "SELECT COUNT(*) FROM Notifications";
                        using (var command = new SQLiteCommand(countQuery, connection))
                        {
                            stats.NotificationCount = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }
                }

                // Get log files count and size
                if (Directory.Exists(_logsDirectory))
                {
                    var logFiles = Directory.GetFiles(_logsDirectory, "*.txt", SearchOption.AllDirectories);
                    stats.LogFileCount = logFiles.Length;
                    stats.LogFilesTotalSize = logFiles.Sum(f => new FileInfo(f).Length);
                }

                stats.LastCleanupTime = DateTime.Now; // This would be stored in a config file in a real implementation
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting cleanup statistics");
            }

            return stats;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cleanupTimer?.Stop();
                _cleanupTimer?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Statistics about the cleanup service
    /// </summary>
    public class CleanupStats
    {
        public int NotificationCount { get; set; }
        public int LogFileCount { get; set; }
        public long LogFilesTotalSize { get; set; }
        public DateTime LastCleanupTime { get; set; }
    }
}
