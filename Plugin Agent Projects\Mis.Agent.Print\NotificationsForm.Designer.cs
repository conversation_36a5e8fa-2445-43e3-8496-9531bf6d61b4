﻿namespace Mis.Agent.Print
{
    partial class NotificationsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl1 = new TabControl();
            NotificationsTab = new TabPage();
            checkBox1 = new CheckBox();
            dataGridView1 = new DataGridView();
            btnClearNotifications = new Button();
            tabControl1.SuspendLayout();
            NotificationsTab.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            SuspendLayout();
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(NotificationsTab);
            tabControl1.Location = new Point(0, 0);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(800, 449);
            tabControl1.TabIndex = 0;
            // 
            // NotificationsTab
            // 
            NotificationsTab.Controls.Add(btnClearNotifications);
            NotificationsTab.Controls.Add(checkBox1);
            NotificationsTab.Controls.Add(dataGridView1);
            NotificationsTab.Location = new Point(4, 29);
            NotificationsTab.Name = "NotificationsTab";
            NotificationsTab.Padding = new Padding(3);
            NotificationsTab.Size = new Size(792, 416);
            NotificationsTab.TabIndex = 0;
            NotificationsTab.Text = "Notifications Tab";
            NotificationsTab.UseVisualStyleBackColor = true;
            // 
            // checkBox1
            // 
            checkBox1.AutoSize = true;
            checkBox1.Checked = true;
            checkBox1.CheckState = CheckState.Checked;
            checkBox1.Location = new Point(25, 6);
            checkBox1.Name = "checkBox1";
            checkBox1.Size = new Size(182, 24);
            checkBox1.TabIndex = 9;
            checkBox1.Text = "تعطيل/تفعيل الإشعارات";
            checkBox1.UseVisualStyleBackColor = true;
            checkBox1.CheckedChanged += checkBox1_CheckedChanged;
            // 
            // dataGridView1
            // 
            dataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView1.Location = new Point(8, 49);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.RowHeadersWidth = 51;
            dataGridView1.RowTemplate.Height = 29;
            dataGridView1.Size = new Size(781, 320);
            dataGridView1.TabIndex = 0;
            //
            // btnClearNotifications
            //
            btnClearNotifications.Location = new Point(650, 375);
            btnClearNotifications.Name = "btnClearNotifications";
            btnClearNotifications.Size = new Size(139, 34);
            btnClearNotifications.TabIndex = 10;
            btnClearNotifications.Text = "Clear Notifications";
            btnClearNotifications.UseVisualStyleBackColor = true;
            btnClearNotifications.Click += BtnClearNotifications_Click;
            // 
            // NotificationsForm
            // 
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 450);
            Controls.Add(tabControl1);
            Name = "NotificationsForm";
            Text = "NotificationsForm";
            tabControl1.ResumeLayout(false);
            NotificationsTab.ResumeLayout(false);
            NotificationsTab.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl1;
        public TabPage NotificationsTab;
        public DataGridView dataGridView1;
        private CheckBox checkBox1;
        private Button btnClearNotifications;
    }
}