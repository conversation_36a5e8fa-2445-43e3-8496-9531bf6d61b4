using System;
using System.Reflection;
using System.Collections.Generic;

namespace Mis.Agent.Services
{
    /// <summary>
    /// Helper class that provides reflection-based access to main application services for plugins
    /// This allows plugins to access main application functionality without assembly references
    /// </summary>
    public static class PluginReflectionHelper
    {
        private static Assembly? _mainAssembly;
        private static Type? _serialPortBridgeType;
        private static Type? _notificationManagerType;

        /// <summary>
        /// Initialize the reflection helper with the main assembly
        /// This should be called by the PluginManager when loading plugins
        /// </summary>
        /// <param name="mainAssembly">The main application assembly</param>
        public static void Initialize(Assembly mainAssembly)
        {
            _mainAssembly = mainAssembly;
            
            try
            {
                // Get types from the main assembly
                _serialPortBridgeType = _mainAssembly.GetType("Mis.Agent.Services.SerialPortBridge");
                _notificationManagerType = _mainAssembly.GetType("Mis.Agent.ApplicationsContext.NotificationManager");
                
                Console.WriteLine("PluginReflectionHelper initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing PluginReflectionHelper: {ex.Message}");
            }
        }

        /// <summary>
        /// Call SerialPortBridge.PublicInitialize method via reflection
        /// </summary>
        public static void CallPublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            try
            {
                if (_serialPortBridgeType == null)
                {
                    throw new InvalidOperationException("PluginReflectionHelper not initialized");
                }

                var method = _serialPortBridgeType.GetMethod("PublicInitialize", 
                    BindingFlags.Public | BindingFlags.Static);
                
                if (method != null)
                {
                    method.Invoke(null, new object[] { barcodeUrl, comPort, isCaptureImageMode });
                }
                else
                {
                    throw new MethodAccessException("PublicInitialize method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling PublicInitialize: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Call SerialPortBridge.StartListening method via reflection
        /// </summary>
        public static void CallStartListening(string comPort)
        {
            try
            {
                if (_serialPortBridgeType == null)
                {
                    throw new InvalidOperationException("PluginReflectionHelper not initialized");
                }

                var method = _serialPortBridgeType.GetMethod("StartListening", 
                    BindingFlags.Public | BindingFlags.Static);
                
                if (method != null)
                {
                    method.Invoke(null, new object[] { comPort });
                }
                else
                {
                    throw new MethodAccessException("StartListening method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling StartListening: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Call SerialPortBridge.ScanWithSelectedScanner method via reflection
        /// </summary>
        public static string CallScanWithSelectedScanner(string scannerName)
        {
            try
            {
                if (_serialPortBridgeType == null)
                {
                    throw new InvalidOperationException("PluginReflectionHelper not initialized");
                }

                var method = _serialPortBridgeType.GetMethod("ScanWithSelectedScanner", 
                    BindingFlags.Public | BindingFlags.Static);
                
                if (method != null)
                {
                    var result = method.Invoke(null, new object[] { scannerName });
                    return result?.ToString() ?? string.Empty;
                }
                else
                {
                    throw new MethodAccessException("ScanWithSelectedScanner method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling ScanWithSelectedScanner: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Call SerialPortBridge.GetAvailableCOMPorts method via reflection
        /// </summary>
        public static string[] CallGetAvailableCOMPorts()
        {
            try
            {
                if (_serialPortBridgeType == null)
                {
                    throw new InvalidOperationException("PluginReflectionHelper not initialized");
                }

                var method = _serialPortBridgeType.GetMethod("GetAvailableCOMPorts", 
                    BindingFlags.Public | BindingFlags.Static);
                
                if (method != null)
                {
                    var result = method.Invoke(null, null);
                    return result as string[] ?? new string[0];
                }
                else
                {
                    throw new MethodAccessException("GetAvailableCOMPorts method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableCOMPorts: {ex.Message}");
                return new string[0];
            }
        }

        /// <summary>
        /// Call SerialPortBridge.GetAvailableScanners method via reflection
        /// </summary>
        public static List<string> CallGetAvailableScanners()
        {
            try
            {
                if (_serialPortBridgeType == null)
                {
                    throw new InvalidOperationException("PluginReflectionHelper not initialized");
                }

                var method = _serialPortBridgeType.GetMethod("GetAvailableScanners", 
                    BindingFlags.Public | BindingFlags.Static);
                
                if (method != null)
                {
                    var result = method.Invoke(null, null);
                    return result as List<string> ?? new List<string>();
                }
                else
                {
                    throw new MethodAccessException("GetAvailableScanners method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableScanners: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Get SerialPortBridge.IsPortOpen property via reflection
        /// </summary>
        public static bool GetIsPortOpen()
        {
            try
            {
                if (_serialPortBridgeType == null)
                {
                    throw new InvalidOperationException("PluginReflectionHelper not initialized");
                }

                var property = _serialPortBridgeType.GetProperty("IsPortOpen", 
                    BindingFlags.Public | BindingFlags.Static);
                
                if (property != null)
                {
                    var result = property.GetValue(null);
                    return result is bool boolResult && boolResult;
                }
                else
                {
                    throw new PropertyAccessException("IsPortOpen property not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting IsPortOpen: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Show notification via the main application's notification system
        /// </summary>
        public static void ShowNotification(string title, string text)
        {
            try
            {
                if (_notificationManagerType == null)
                {
                    Console.WriteLine($"Notification: {title} - {text}");
                    return;
                }

                // Try to find and call the notification method
                var method = _notificationManagerType.GetMethod("Notify", 
                    BindingFlags.Public | BindingFlags.Static);
                
                if (method != null)
                {
                    // Create notification args - this might need adjustment based on your NotificationManager implementation
                    var notificationArgsType = _mainAssembly?.GetType("Mis.Agent.ApplicationsContext.NotificationEventArgs");
                    if (notificationArgsType != null)
                    {
                        var notificationArgs = Activator.CreateInstance(notificationArgsType);
                        var isEnabledProperty = notificationArgsType.GetProperty("IsEnabled");
                        isEnabledProperty?.SetValue(notificationArgs, true);
                        
                        method.Invoke(null, new object[] { notificationArgs });
                    }
                }
                
                // Fallback to console output
                Console.WriteLine($"Plugin Notification: {title} - {text}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error showing notification: {ex.Message}");
                Console.WriteLine($"Fallback Notification: {title} - {text}");
            }
        }
    }

    /// <summary>
    /// Custom exception for property access errors
    /// </summary>
    public class PropertyAccessException : Exception
    {
        public PropertyAccessException(string message) : base(message) { }
        public PropertyAccessException(string message, Exception innerException) : base(message, innerException) { }
    }
}
