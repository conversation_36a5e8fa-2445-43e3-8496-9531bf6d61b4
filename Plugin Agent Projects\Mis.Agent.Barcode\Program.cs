
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Windows.Forms;

namespace Mis.Agent.Barcode
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Initialize the application configuration
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Build the service provider
            var serviceProvider = ConfigureServices();

            // Resolve the main form
            var mainForm = serviceProvider.GetRequiredService<ScannerForm>();

            // Run the application
            Application.Run(mainForm);
        }

        private static IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection();
            // In your host builder or Startup.ConfigureServices:
            services.AddTransient<IBarcodeAppService, BarcodeService>();
            //services.AddTransient<IPublicBarcodeAppService, BarcodeService>();
            services.AddTransient<IScannerAppService, ScannerService>();
            //services.AddTransient<IPublicScannerAppService, ScannerService>();

            services.AddSingleton<ScannerForm>(); // Register your main form

            return services.BuildServiceProvider();
        }
    }
}
