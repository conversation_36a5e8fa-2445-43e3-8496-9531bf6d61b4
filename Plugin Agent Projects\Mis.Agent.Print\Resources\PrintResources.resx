<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0,
      Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0,
      Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Form Titles -->
  <data name="PrintFormTitle" xml:space="preserve">
    <value>Print Configuration</value>
  </data>
  <data name="NotificationsFormTitle" xml:space="preserve">
    <value>Notifications Management</value>
  </data>
  <!-- Tab Names -->
  <data name="PrintTabText" xml:space="preserve">
    <value>Print Settings</value>
  </data>
  <data name="NotificationsTabText" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <!-- Labels -->
  <data name="PrinterNameLabel" xml:space="preserve">
    <value>Printer Name:</value>
  </data>
  <data name="PaperSizeLabel" xml:space="preserve">
    <value>Paper Size:</value>
  </data>
  <data name="DefaultPrinterLabel" xml:space="preserve">
    <value>Default Printer:</value>
  </data>
  <data name="CurrentPrinterLabel" xml:space="preserve">
    <value>Current Printer:</value>
  </data>
  <data name="CurrentPaperSizeLabel" xml:space="preserve">
    <value>Current Paper Size:</value>
  </data>
  <!-- Buttons -->
  <data name="SavePrintConfigButton" xml:space="preserve">
    <value>Save Print Configuration</value>
  </data>
  <data name="TestPrintButton" xml:space="preserve">
    <value>Test Print</value>
  </data>
  <data name="ClearNotificationsButton" xml:space="preserve">
    <value>Clear All Notifications</value>
  </data>
  <!-- Checkboxes -->
  <data name="EnableNotificationsCheckbox" xml:space="preserve">
    <value>Enable/Disable Notifications</value>
  </data>
  <!-- Column Headers -->
  <data name="NotificationIdColumn" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="NotificationNumberColumn" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="IsPrintedColumn" xml:space="preserve">
    <value>Is Printed</value>
  </data>
  <data name="ReceiveTimeColumn" xml:space="preserve">
    <value>Receive Time</value>
  </data>
  <!-- Messages -->
  <data name="PrintConfigSavedMessage" xml:space="preserve">
    <value>Print configuration saved successfully!</value>
  </data>
  <data name="PrintConfigFailedMessage" xml:space="preserve">
    <value>Failed to save print configuration.</value>
  </data>
  <data name="TestPrintSuccessMessage" xml:space="preserve">
    <value>Test print completed successfully!</value>
  </data>
  <data name="TestPrintFailedMessage" xml:space="preserve">
    <value>Test print failed. Please check printer settings.</value>
  </data>
  <data name="NotificationsClearedMessage" xml:space="preserve">
    <value>All notifications cleared successfully!</value>
  </data>
  <data name="ClearNotificationsFailedMessage" xml:space="preserve">
    <value>Failed to clear notifications.</value>
  </data>
  <data name="ClearNotificationsConfirmMessage" xml:space="preserve">
    <value>Are you sure you want to clear all notifications?</value>
  </data>
  <data name="NotificationsEnabledMessage" xml:space="preserve">
    <value>Notifications enabled.</value>
  </data>
  <data name="NotificationsDisabledMessage" xml:space="preserve">
    <value>Notifications disabled.</value>
  </data>
  <data name="NoPrinterSelectedMessage" xml:space="preserve">
    <value>Please select a printer.</value>
  </data>
  <data name="PrinterNotFoundMessage" xml:space="preserve">
    <value>Selected printer not found.</value>
  </data>
  <data name="Properties" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="PaperType" xml:space="preserve">
    <value>Paper Type</value>
  </data>
</root>