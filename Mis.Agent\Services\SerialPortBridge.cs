using System;
using System.Reflection;

namespace Mis.Agent.Services
{
    /// <summary>
    /// Bridge service that allows plugins to access SerialPortManager methods through reflection
    /// This eliminates the need for assembly references between plugins and the main application
    /// </summary>
    public static class SerialPortBridge
    {
        private static readonly Type _serialPortManagerType;
        private static readonly object _serialPortManagerInstance;
        private static readonly MethodInfo _publicInitializeMethod;
        private static readonly MethodInfo _startListeningMethod;
        private static readonly MethodInfo _scanWithSelectedScannerMethod;
        private static readonly MethodInfo _getAvailableCOMPortsMethod;
        private static readonly MethodInfo _getAvailableScannersMethod;
        private static readonly MethodInfo _initializeHubConnectionMethod;
        private static readonly MethodInfo _captureImageAsyncMethod;
        private static readonly MethodInfo _writeMethod;
        private static readonly MethodInfo _forceReleasePortMethod;
        private static readonly MethodInfo _stopListeningMethod;
        private static readonly PropertyInfo _isPortOpenProperty;
        private static readonly PropertyInfo _isCaptureImageModeProperty;
        private static readonly PropertyInfo _lastScannedBase64DataProperty;

        static SerialPortBridge()
        {
            try
            {
                // Get the SerialPortManager type and instance
                _serialPortManagerType = typeof(SerialPortManager);
                _serialPortManagerInstance = SerialPortManager.Instance;

                // Cache method info for better performance
                _publicInitializeMethod = _serialPortManagerType.GetMethod("PublicInitialize",
                    new Type[] { typeof(string), typeof(string), typeof(bool) });

                _startListeningMethod = _serialPortManagerType.GetMethod("StartListening",
                    new Type[] { typeof(string) });

                _scanWithSelectedScannerMethod = _serialPortManagerType.GetMethod("ScanWithSelectedScanner",
                    new Type[] { typeof(string) });

                _getAvailableCOMPortsMethod = _serialPortManagerType.GetMethod("GetAvailableCOMPorts",
                    Type.EmptyTypes);

                _getAvailableScannersMethod = _serialPortManagerType.GetMethod("GetAvailableScanners",
                    Type.EmptyTypes);

                _initializeHubConnectionMethod = _serialPortManagerType.GetMethod("InitializeHubConnection",
                    new Type[] { typeof(string) });

                _captureImageAsyncMethod = _serialPortManagerType.GetMethod("CaptureImageAsync",
                    new Type[] { typeof(string) });

                _writeMethod = _serialPortManagerType.GetMethod("Write",
                    new Type[] { typeof(string) });

                _forceReleasePortMethod = _serialPortManagerType.GetMethod("ForceReleasePort",
                    Type.EmptyTypes);

                _stopListeningMethod = _serialPortManagerType.GetMethod("StopListening",
                    Type.EmptyTypes);

                _isPortOpenProperty = _serialPortManagerType.GetProperty("IsPortOpen");
                _isCaptureImageModeProperty = _serialPortManagerType.GetProperty("IsCaptureImageMode");
                _lastScannedBase64DataProperty = _serialPortManagerType.GetProperty("LastScannedBase64Data");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing SerialPortBridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Initialize barcode functionality with the specified parameters
        /// </summary>
        /// <param name="barcodeUrl">The barcode service URL</param>
        /// <param name="comPort">The COM port to use</param>
        /// <param name="isCaptureImageMode">Whether to enable image capture mode</param>
        public static void PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            try
            {
                _publicInitializeMethod?.Invoke(_serialPortManagerInstance,
                    new object[] { barcodeUrl, comPort, isCaptureImageMode });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling PublicInitialize via bridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start listening on the specified COM port
        /// </summary>
        /// <param name="comPort">The COM port to listen on</param>
        public static void StartListening(string comPort)
        {
            try
            {
                _startListeningMethod?.Invoke(_serialPortManagerInstance, new object[] { comPort });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling StartListening via bridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Scan with the selected scanner
        /// </summary>
        /// <param name="scannerName">The name of the scanner to use</param>
        /// <returns>Base64 encoded scanned image</returns>
        public static string ScanWithSelectedScanner(string scannerName)
        {
            try
            {
                var result = _scanWithSelectedScannerMethod?.Invoke(_serialPortManagerInstance,
                    new object[] { scannerName });
                return result?.ToString() ?? string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling ScanWithSelectedScanner via bridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get available COM ports
        /// </summary>
        /// <returns>Array of available COM port names</returns>
        public static string[] GetAvailableCOMPorts()
        {
            try
            {
                var result = _getAvailableCOMPortsMethod?.Invoke(_serialPortManagerInstance, null);
                return result as string[] ?? new string[0];
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableCOMPorts via bridge: {ex.Message}");
                return new string[0];
            }
        }

        /// <summary>
        /// Get available scanners
        /// </summary>
        /// <returns>List of available scanner names</returns>
        public static System.Collections.Generic.List<string> GetAvailableScanners()
        {
            try
            {
                var result = _getAvailableScannersMethod?.Invoke(_serialPortManagerInstance, null);
                return result as System.Collections.Generic.List<string> ?? new System.Collections.Generic.List<string>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableScanners via bridge: {ex.Message}");
                return new System.Collections.Generic.List<string>();
            }
        }

        /// <summary>
        /// Check if the serial port is currently open
        /// </summary>
        /// <returns>True if port is open, false otherwise</returns>
        public static bool IsPortOpen
        {
            get
            {
                try
                {
                    var result = _isPortOpenProperty?.GetValue(_serialPortManagerInstance);
                    return result is bool boolResult && boolResult;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting IsPortOpen via bridge: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Subscribe to SerialPortManager events using reflection
        /// This allows plugins to receive notifications without direct references
        /// </summary>
        /// <param name="eventName">Name of the event to subscribe to</param>
        /// <param name="handler">The event handler delegate</param>
        public static void SubscribeToEvent(string eventName, Delegate handler)
        {
            try
            {
                var eventInfo = _serialPortManagerType.GetEvent(eventName);
                if (eventInfo != null)
                {
                    eventInfo.AddEventHandler(_serialPortManagerInstance, handler);
                    Console.WriteLine($"Successfully subscribed to event: {eventName}");
                }
                else
                {
                    Console.WriteLine($"Event not found: {eventName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error subscribing to event {eventName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Unsubscribe from SerialPortManager events using reflection
        /// </summary>
        /// <param name="eventName">Name of the event to unsubscribe from</param>
        /// <param name="handler">The event handler delegate</param>
        public static void UnsubscribeFromEvent(string eventName, Delegate handler)
        {
            try
            {
                var eventInfo = _serialPortManagerType.GetEvent(eventName);
                if (eventInfo != null)
                {
                    eventInfo.RemoveEventHandler(_serialPortManagerInstance, handler);
                    Console.WriteLine($"Successfully unsubscribed from event: {eventName}");
                }
                else
                {
                    Console.WriteLine($"Event not found: {eventName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error unsubscribing from event {eventName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Initialize hub connection
        /// </summary>
        /// <param name="hubUrl">The hub URL to connect to</param>
        public static void InitializeHubConnection(string hubUrl)
        {
            try
            {
                _initializeHubConnectionMethod?.Invoke(_serialPortManagerInstance, new object[] { hubUrl });
                Console.WriteLine($"InitializeHubConnection called successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling InitializeHubConnection: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Capture image asynchronously
        /// </summary>
        /// <param name="comPort">The COM port to use for image capture</param>
        public static void CaptureImageAsync(string comPort)
        {
            try
            {
                _captureImageAsyncMethod?.Invoke(_serialPortManagerInstance, new object[] { comPort });
                Console.WriteLine($"CaptureImageAsync called successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling CaptureImageAsync: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Write command to serial port
        /// </summary>
        /// <param name="command">The command to write</param>
        public static void Write(string command)
        {
            try
            {
                _writeMethod?.Invoke(_serialPortManagerInstance, new object[] { command });
                Console.WriteLine($"Write command called successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling Write: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Force release the serial port
        /// </summary>
        public static void ForceReleasePort()
        {
            try
            {
                _forceReleasePortMethod?.Invoke(_serialPortManagerInstance, null);
                Console.WriteLine($"ForceReleasePort called successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling ForceReleasePort: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop listening on the serial port
        /// </summary>
        public static void StopListening()
        {
            try
            {
                _stopListeningMethod?.Invoke(_serialPortManagerInstance, null);
                Console.WriteLine($"StopListening called successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling StopListening: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get or set capture image mode
        /// </summary>
        public static bool IsCaptureImageMode
        {
            get
            {
                try
                {
                    var result = _isCaptureImageModeProperty?.GetValue(_serialPortManagerInstance);
                    return result is bool boolResult && boolResult;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting IsCaptureImageMode: {ex.Message}");
                    return false;
                }
            }
            set
            {
                try
                {
                    _isCaptureImageModeProperty?.SetValue(_serialPortManagerInstance, value);
                    Console.WriteLine($"IsCaptureImageMode set to {value}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting IsCaptureImageMode: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get the last scanned base64 data
        /// </summary>
        public static string LastScannedBase64Data
        {
            get
            {
                try
                {
                    var result = _lastScannedBase64DataProperty?.GetValue(_serialPortManagerInstance);
                    return result?.ToString() ?? string.Empty;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting LastScannedBase64Data: {ex.Message}");
                    return string.Empty;
                }
            }
            set
            {
                try
                {
                    _lastScannedBase64DataProperty?.SetValue(_serialPortManagerInstance, value);
                    Console.WriteLine($"LastScannedBase64Data set");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting LastScannedBase64Data: {ex.Message}");
                }
            }
        }
    }
}
