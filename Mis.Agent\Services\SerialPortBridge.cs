using System;
using System.Reflection;

namespace Mis.Agent.Services
{
    /// <summary>
    /// Bridge service that allows plugins to access SerialPortManager methods through reflection
    /// This eliminates the need for assembly references between plugins and the main application
    /// </summary>
    public static class SerialPortBridge
    {
        private static readonly Type _serialPortManagerType;
        private static readonly object _serialPortManagerInstance;
        private static readonly MethodInfo _publicInitializeMethod;
        private static readonly MethodInfo _startListeningMethod;
        private static readonly MethodInfo _scanWithSelectedScannerMethod;
        private static readonly MethodInfo _getAvailableCOMPortsMethod;
        private static readonly MethodInfo _getAvailableScannersMethod;
        private static readonly PropertyInfo _isPortOpenProperty;

        static SerialPortBridge()
        {
            try
            {
                // Get the SerialPortManager type and instance
                _serialPortManagerType = typeof(SerialPortManager);
                _serialPortManagerInstance = SerialPortManager.Instance;

                // Cache method info for better performance
                _publicInitializeMethod = _serialPortManagerType.GetMethod("PublicInitialize", 
                    new Type[] { typeof(string), typeof(string), typeof(bool) });
                
                _startListeningMethod = _serialPortManagerType.GetMethod("StartListening", 
                    new Type[] { typeof(string) });
                
                _scanWithSelectedScannerMethod = _serialPortManagerType.GetMethod("ScanWithSelectedScanner", 
                    new Type[] { typeof(string) });
                
                _getAvailableCOMPortsMethod = _serialPortManagerType.GetMethod("GetAvailableCOMPorts", 
                    Type.EmptyTypes);
                
                _getAvailableScannersMethod = _serialPortManagerType.GetMethod("GetAvailableScanners", 
                    Type.EmptyTypes);
                
                _isPortOpenProperty = _serialPortManagerType.GetProperty("IsPortOpen");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing SerialPortBridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Initialize barcode functionality with the specified parameters
        /// </summary>
        /// <param name="barcodeUrl">The barcode service URL</param>
        /// <param name="comPort">The COM port to use</param>
        /// <param name="isCaptureImageMode">Whether to enable image capture mode</param>
        public static void PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            try
            {
                _publicInitializeMethod?.Invoke(_serialPortManagerInstance, 
                    new object[] { barcodeUrl, comPort, isCaptureImageMode });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling PublicInitialize via bridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start listening on the specified COM port
        /// </summary>
        /// <param name="comPort">The COM port to listen on</param>
        public static void StartListening(string comPort)
        {
            try
            {
                _startListeningMethod?.Invoke(_serialPortManagerInstance, new object[] { comPort });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling StartListening via bridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Scan with the selected scanner
        /// </summary>
        /// <param name="scannerName">The name of the scanner to use</param>
        /// <returns>Base64 encoded scanned image</returns>
        public static string ScanWithSelectedScanner(string scannerName)
        {
            try
            {
                var result = _scanWithSelectedScannerMethod?.Invoke(_serialPortManagerInstance, 
                    new object[] { scannerName });
                return result?.ToString() ?? string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling ScanWithSelectedScanner via bridge: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get available COM ports
        /// </summary>
        /// <returns>Array of available COM port names</returns>
        public static string[] GetAvailableCOMPorts()
        {
            try
            {
                var result = _getAvailableCOMPortsMethod?.Invoke(_serialPortManagerInstance, null);
                return result as string[] ?? new string[0];
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableCOMPorts via bridge: {ex.Message}");
                return new string[0];
            }
        }

        /// <summary>
        /// Get available scanners
        /// </summary>
        /// <returns>List of available scanner names</returns>
        public static System.Collections.Generic.List<string> GetAvailableScanners()
        {
            try
            {
                var result = _getAvailableScannersMethod?.Invoke(_serialPortManagerInstance, null);
                return result as System.Collections.Generic.List<string> ?? new System.Collections.Generic.List<string>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableScanners via bridge: {ex.Message}");
                return new System.Collections.Generic.List<string>();
            }
        }

        /// <summary>
        /// Check if the serial port is currently open
        /// </summary>
        /// <returns>True if port is open, false otherwise</returns>
        public static bool IsPortOpen
        {
            get
            {
                try
                {
                    var result = _isPortOpenProperty?.GetValue(_serialPortManagerInstance);
                    return result is bool boolResult && boolResult;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting IsPortOpen via bridge: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Subscribe to SerialPortManager events using reflection
        /// This allows plugins to receive notifications without direct references
        /// </summary>
        /// <param name="eventName">Name of the event to subscribe to</param>
        /// <param name="handler">The event handler delegate</param>
        public static void SubscribeToEvent(string eventName, Delegate handler)
        {
            try
            {
                var eventInfo = _serialPortManagerType.GetEvent(eventName);
                if (eventInfo != null)
                {
                    eventInfo.AddEventHandler(_serialPortManagerInstance, handler);
                    Console.WriteLine($"Successfully subscribed to event: {eventName}");
                }
                else
                {
                    Console.WriteLine($"Event not found: {eventName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error subscribing to event {eventName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Unsubscribe from SerialPortManager events using reflection
        /// </summary>
        /// <param name="eventName">Name of the event to unsubscribe from</param>
        /// <param name="handler">The event handler delegate</param>
        public static void UnsubscribeFromEvent(string eventName, Delegate handler)
        {
            try
            {
                var eventInfo = _serialPortManagerType.GetEvent(eventName);
                if (eventInfo != null)
                {
                    eventInfo.RemoveEventHandler(_serialPortManagerInstance, handler);
                    Console.WriteLine($"Successfully unsubscribed from event: {eventName}");
                }
                else
                {
                    Console.WriteLine($"Event not found: {eventName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error unsubscribing from event {eventName}: {ex.Message}");
            }
        }
    }
}
