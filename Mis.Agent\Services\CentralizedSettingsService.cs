using System.Text.Json;
using Microsoft.Extensions.Configuration;

namespace Mis.Agent.Services
{
    /// <summary>
    /// Centralized service for managing all application settings
    /// </summary>
    public class CentralizedSettingsService
    {
        private readonly string _appSettingsPath;
        private readonly object _lock = new object();

        public CentralizedSettingsService()
        {
            _appSettingsPath = Path.Combine(AppContext.BaseDirectory, "appsettings.json");
        }

        /// <summary>
        /// Saves all plugin settings to appsettings.json
        /// </summary>
        public async Task SaveAllSettingsAsync(Dictionary<string, object> allSettings)
        {
            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        // Read current appsettings.json
                        var currentSettings = ReadCurrentSettings();

                        // Update settings based on collected plugin data
                        UpdateSettingsFromPlugins(currentSettings, allSettings);

                        // Write back to appsettings.json
                        WriteSettingsToFile(currentSettings);
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException($"Failed to save settings: {ex.Message}", ex);
                    }
                }
            });
        }

        /// <summary>
        /// Reads current settings from appsettings.json
        /// </summary>
        private Dictionary<string, object> ReadCurrentSettings()
        {
            try
            {
                if (!File.Exists(_appSettingsPath))
                {
                    return new Dictionary<string, object>();
                }

                var json = File.ReadAllText(_appSettingsPath);
                var settings = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                return settings ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading current settings: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Updates settings dictionary with plugin data
        /// </summary>
        private void UpdateSettingsFromPlugins(Dictionary<string, object> currentSettings, Dictionary<string, object> pluginSettings)
        {
            foreach (var pluginSetting in pluginSettings)
            {
                var tabName = pluginSetting.Key;
                var settings = pluginSetting.Value as Dictionary<string, object>;

                if (settings == null) continue;

                switch (tabName)
                {
                    case "PrintTab":
                        UpdatePrintSettings(currentSettings, settings);
                        break;
                    case "BarcodeTab":
                        UpdateBarcodeSettings(currentSettings, settings);
                        break;
                    case "ScannerTab":
                        UpdateScannerSettings(currentSettings, settings);
                        break;
                    case "PortTab":
                        UpdatePortSettings(currentSettings, settings);
                        break;
                }
            }
        }

        private void UpdatePrintSettings(Dictionary<string, object> currentSettings, Dictionary<string, object> printSettings)
        {
            if (!currentSettings.ContainsKey("PrinterEntity"))
            {
                currentSettings["PrinterEntity"] = new Dictionary<string, object>();
            }

            var printerEntity = currentSettings["PrinterEntity"] as Dictionary<string, object> 
                ?? JsonSerializer.Deserialize<Dictionary<string, object>>(currentSettings["PrinterEntity"].ToString());

            if (printSettings.ContainsKey("DefaultPrinter"))
            {
                printerEntity["DefaultPrinter"] = printSettings["DefaultPrinter"];
            }

            if (printSettings.ContainsKey("DefaultPaperSize"))
            {
                printerEntity["DefaultPaperSize"] = printSettings["DefaultPaperSize"];
            }

            currentSettings["PrinterEntity"] = printerEntity;
        }

        private void UpdateBarcodeSettings(Dictionary<string, object> currentSettings, Dictionary<string, object> barcodeSettings)
        {
            if (!currentSettings.ContainsKey("Barcode"))
            {
                currentSettings["Barcode"] = new Dictionary<string, object>();
            }

            var barcode = currentSettings["Barcode"] as Dictionary<string, object>
                ?? JsonSerializer.Deserialize<Dictionary<string, object>>(currentSettings["Barcode"].ToString());

            if (barcodeSettings.ContainsKey("BarcodeBaseUrl"))
            {
                barcode["BarcodeBaseUrl"] = barcodeSettings["BarcodeBaseUrl"];
            }

            currentSettings["Barcode"] = barcode;
        }

        private void UpdateScannerSettings(Dictionary<string, object> currentSettings, Dictionary<string, object> scannerSettings)
        {
            if (!currentSettings.ContainsKey("DefaultScanner"))
            {
                currentSettings["DefaultScanner"] = new Dictionary<string, object>();
            }

            var scanner = currentSettings["DefaultScanner"] as Dictionary<string, object>
                ?? JsonSerializer.Deserialize<Dictionary<string, object>>(currentSettings["DefaultScanner"].ToString());

            if (scannerSettings.ContainsKey("Scanner"))
            {
                scanner["Scanner"] = scannerSettings["Scanner"];
            }

            if (scannerSettings.ContainsKey("IsScanByBarcodeReader"))
            {
                scanner["IsScanByBarcodeReader"] = scannerSettings["IsScanByBarcodeReader"];
            }

            currentSettings["DefaultScanner"] = scanner;
        }

        private void UpdatePortSettings(Dictionary<string, object> currentSettings, Dictionary<string, object> portSettings)
        {
            if (!currentSettings.ContainsKey("Server"))
            {
                currentSettings["Server"] = new Dictionary<string, object>();
            }

            var server = currentSettings["Server"] as Dictionary<string, object>
                ?? JsonSerializer.Deserialize<Dictionary<string, object>>(currentSettings["Server"].ToString());

            if (portSettings.ContainsKey("BaseUrl"))
            {
                server["BaseUrl"] = portSettings["BaseUrl"];
            }

            currentSettings["Server"] = server;
        }

        /// <summary>
        /// Writes settings to appsettings.json file
        /// </summary>
        private void WriteSettingsToFile(Dictionary<string, object> settings)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = null
                };

                var json = JsonSerializer.Serialize(settings, options);
                File.WriteAllText(_appSettingsPath, json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to write settings to file: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets a specific setting value
        /// </summary>
        public T GetSetting<T>(string section, string key, T defaultValue = default(T))
        {
            try
            {
                var settings = ReadCurrentSettings();
                
                if (settings.ContainsKey(section))
                {
                    var sectionData = settings[section] as Dictionary<string, object>
                        ?? JsonSerializer.Deserialize<Dictionary<string, object>>(settings[section].ToString());
                    
                    if (sectionData.ContainsKey(key))
                    {
                        return JsonSerializer.Deserialize<T>(sectionData[key].ToString());
                    }
                }
                
                return defaultValue;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting setting {section}.{key}: {ex.Message}");
                return defaultValue;
            }
        }
    }
}
