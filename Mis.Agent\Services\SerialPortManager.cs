using Microsoft.AspNetCore.SignalR.Client;
using System;
using System.Data.SQLite;
using System.Drawing;
using System.IO.Ports;
using System.Text;
using WIA;

namespace Mis.Agent.Services
{
    public class SerialPortManager
    {
        private static readonly SerialPortManager _instance = new SerialPortManager();
        public SerialPort _serialPort;
        public bool IsCaptureImageMode { get; set; } // Property to store the capture mode state
        private HubConnection _hubConnection; // Added HubConnection field
        private bool? _isCaptureImageMode;
        private string _databaseFile;

        public string LastScannedBase64Data { get; set; }
        public event Action<Image> BarcodeImageCaptured; // Define an event
        public event Action<Image> scannerImageCaptured; // Define an event
        public event Action NotificationUpdated;

        private SerialPortManager()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _databaseFile = Path.Combine(baseDirectory, "notifications.db");
        }

        #region Barcode 
        public static SerialPortManager Instance => _instance;
        public bool IsPortOpen => _serialPort != null && _serialPort.IsOpen;

        public void StartListening(string comPort = null)
        {
            try
            {
                if (IsPortOpen)
                {
                    // Already open: consider logging or handling this case
                    return;
                }
                // Check if the specified COM port is available
                if (!Array.Exists(SerialPort.GetPortNames(), port => port.Equals(comPort, StringComparison.OrdinalIgnoreCase)))
                {
                    Console.WriteLine($"COM port {comPort} is not available.");
                    return;
                }
                // Force release the port
                ForceReleasePort();
                try
                {
                    // Create a new SerialPort instance
                    _serialPort = new SerialPort(comPort, 9600, Parity.None, 8, StopBits.One);
                    // Subscribe to the DataReceived event
                    _serialPort.DataReceived += SerialPort_DataReceived;

                    // Open the serial port
                    _serialPort.Open();
                    return; // Success
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error opening COM port: {ex.Message}");
                    return;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        public void InitializeHubConnection(string text)
        {
            try
            {
                _hubConnection = new HubConnectionBuilder().WithUrl(text).Build();

                _hubConnection.StartAsync().ContinueWith(task =>
                {
                    if (task.IsFaulted)
                    {
                        Console.WriteLine($"Failed to connect to Hub: {task.Exception?.GetBaseException().Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// Public method for plugins to initialize barcode functionality
        /// This method can be called via reflection from plugins
        /// </summary>
        public void PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            try
            {
                if (string.IsNullOrEmpty(comPort))
                    throw new ArgumentException("COM Port cannot be null or empty", nameof(comPort));
                if (string.IsNullOrEmpty(barcodeUrl))
                    throw new ArgumentException("Base URL cannot be null or empty", nameof(barcodeUrl));

                // Set capture mode
                IsCaptureImageMode = isCaptureImageMode;

                // Initialize hub connection
                InitializeHubConnection(barcodeUrl);

                // Start listening on the specified COM port
                StartListening(comPort);

                Console.WriteLine($"SerialPortManager initialized with URL: {barcodeUrl}, COM Port: {comPort}, Capture Mode: {isCaptureImageMode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in PublicInitialize: {ex.Message}");
                throw;
            }
        }

        private async void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (sender is SerialPort port)
                {
                    // Check if capture image mode is active
                    if (IsCaptureImageMode)
                    {
                        SetCaptureImageMode(false);
                        // If no bytes are available, exit
                        if (port.BytesToRead <= 0)
                            return;

                        // Read all available bytes
                        byte[] dataBytes = new byte[port.BytesToRead];
                        port.Read(dataBytes, 0, dataBytes.Length);

                        // Convert bytes to Base64 string
                        string base64Data = Convert.ToBase64String(dataBytes);
                        LastScannedBase64Data = base64Data;

                        // Convert bytes to image and trigger the event
                        using (var ms = new MemoryStream(dataBytes))
                        {
                            try
                            {
                                Image image = Image.FromStream(ms);
                                BarcodeImageCaptured?.Invoke(image); // Trigger the event
                            }
                            catch (ArgumentException)
                            {
                                Console.WriteLine("Received data is not a valid image format.");
                            }
                        }
                    }
                    else
                    {
                        // Barcode reading logic
                        byte[] dataBytes = new byte[port.BytesToRead];
                        port.Read(dataBytes, 0, dataBytes.Length);
                        string data = DecodeWindows1256(dataBytes);
                        if (!string.IsNullOrWhiteSpace(data))
                        {
                            // Check the connection and send data
                            if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                            {
                                Console.WriteLine("data " + data);
                                await _hubConnection.InvokeAsync("SendMessage", data);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading data: {ex.Message}");
            }
        }

        public Task CaptureImageAsync(string comPort)
        {
            // Construct the command to capture the image
            string command = "\x16M\r" + imgCapture.hwPictureCmd;
            // Write the command to the serial port
            Write(command);
            return Task.CompletedTask;
        }

        private string DecodeWindows1256(byte[] bytes)
        {
            Encoding windows1256 = Encoding.GetEncoding("windows-1256");
            return windows1256.GetString(bytes).Trim();
        }

        public void ForceReleasePort()
        {
            if (_serialPort != null)
            {
                try
                {
                    // Unsubscribe from the event
                    _serialPort.DataReceived -= SerialPort_DataReceived;

                    // Close the port if it's open
                    if (_serialPort.IsOpen)
                    {
                        _serialPort.Close();
                    }

                    // Use reflection to access the BaseStream and force it to close
                    var baseStream = _serialPort.BaseStream;
                    if (baseStream != null)
                    {
                        baseStream.Close();
                        baseStream.Dispose();
                    }

                    // Dispose of the SerialPort
                    _serialPort.Dispose();
                    _serialPort = null; // Set to null to avoid reuse
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error releasing COM port: {ex.Message}");
                }
            }
        }

        public void SetCaptureImageMode(bool mode)
        {
            IsCaptureImageMode = mode; // Set the current capturing mode
        }

        public void Write(string command)
        {
            if (_serialPort != null && _serialPort.IsOpen)
            {
                _serialPort.Write(command);
            }
            else
            {
                throw new InvalidOperationException("Cannot write to the port because it is not open.");
            }
        }

        public string[] GetAvailableCOMPorts()
        {
            return SerialPort.GetPortNames(); // Return available COM ports
        }
        #endregion

        #region Scanner
        public string ScanWithSelectedScanner(string scannerName)
        {
            if (string.IsNullOrWhiteSpace(scannerName))
            {
                throw new ArgumentException("Scanner name cannot be null or empty.", nameof(scannerName));
            }

            try
            {
                var deviceManager = new DeviceManager();
                var devices = deviceManager.DeviceInfos;

                foreach (DeviceInfo device in devices)
                {
                    if (device.Type == WiaDeviceType.ScannerDeviceType && device.Properties["Name"].get_Value().ToString() == scannerName)
                    {
                        var wiaDevice = device.Connect();
                        var wiaItem = wiaDevice.Items[1];

                        var imageFile = (ImageFile)wiaItem.Transfer(FormatID.wiaFormatJPEG);
                        byte[] imageBytes = (byte[])imageFile.FileData.get_BinaryData();

                        scannerImageCaptured?.Invoke(Image.FromStream(new MemoryStream(imageBytes)));
                        return Convert.ToBase64String(imageBytes);
                    }
                }

                throw new InvalidOperationException($"Scanner '{scannerName}' not found.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error scanning with scanner '{scannerName}': {ex.Message}");
                throw;
            }
        }

        public List<string> GetAvailableScanners()
        {
            var scanners = new List<string>();
            try
            {
                var deviceManager = new DeviceManager();
                foreach (DeviceInfo device in deviceManager.DeviceInfos)
                {
                    if (device.Type == WiaDeviceType.ScannerDeviceType)
                    {
                        scanners.Add(device.Properties["Name"].get_Value().ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting available scanners: {ex.Message}");
            }
            return scanners;
        }
        #endregion

        #region Database Operations
        public async Task<List<Models.TransactionDto>> GetAllNotificationsAsync()
        {
            var notifications = new List<Models.TransactionDto>();

            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile}"))
                {
                    await connection.OpenAsync();

                    var query = "SELECT Id, No, HtmlContent, IsPrinted, ReceiveTime FROM Notifications ORDER BY ReceiveTime DESC";
                    using (var command = new SQLiteCommand(query, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            notifications.Add(new Models.TransactionDto
                            {
                                Id = reader["Id"].ToString(),
                                No = reader["No"].ToString(),
                                HtmlContent = reader["HtmlContent"].ToString(),
                                IsPrinted = Convert.ToBoolean(reader["IsPrinted"]),
                                ReceiveTime = Convert.ToDateTime(reader["ReceiveTime"])
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting notifications: {ex.Message}");
            }

            return notifications;
        }
        #endregion
    }
}
