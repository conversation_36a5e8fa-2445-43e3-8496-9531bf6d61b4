﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
  </PropertyGroup>


  <Target Name="CreatePluginsDirectory" AfterTargets="Build">
    <PropertyGroup>
      <PluginDir>$(OutputPath)Plugins\</PluginDir>
    </PropertyGroup>
    <MakeDir Directories="$(PluginDir)" Condition="!Exists('$(PluginDir)')" />
  </Target>


  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Owin" Version="9.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.6" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="9.0.6" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="System.Security.Cryptography.Pkcs" Version="9.0.6" />
    <PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.0.6" />
    <PackageReference Include="PdfiumViewer.Core" Version="1.0.4" />

    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />


  </ItemGroup>
  <ItemGroup>
    <Reference Include="Interop.WIA">
      <HintPath>WIA\Interop.WIA.dll</HintPath>
    </Reference>
    <Reference Include="IronPdf.Core">
      <HintPath>IronPdf.Core\IronPdf.Core.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <!-- Include ALL JSON files (including appsettings.json) -->
    <None Include="**\*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>

    <!-- Include ALL database files -->
    <None Include="**\*.db">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
  </ItemGroup>


  <ItemGroup>
    <Compile Update="Localization\Messages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Messages.resx</DependentUpon>
    </Compile>
  </ItemGroup>


  <ItemGroup>
    <EmbeddedResource Update="Localization\Messages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Messages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>


  <Target Name="PostPublishSigning" AfterTargets="Publish"
    Condition="'$(Configuration)' == 'Release'">
    <Exec
      Command="powershell -ExecutionPolicy Bypass -File &quot;$(ProjectDir)sign-and-publish.ps1&quot;" />
  </Target>


</Project>