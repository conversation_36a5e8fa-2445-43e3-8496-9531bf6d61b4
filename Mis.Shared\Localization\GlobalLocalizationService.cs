using System.Globalization;
using System.Resources;

namespace Mis.Shared.Localization
{
    public static class GlobalLocalizationService
    {
        private static ILocalizationManager _instance;
        private static readonly object _lock = new object();

        public static ILocalizationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LocalizationManager();
                            InitializeDefaultResources();
                        }
                    }
                }
                return _instance;
            }
        }

        private static void InitializeDefaultResources()
        {
            try
            {
                // Register main application resources
                var mainResourceManager = new ResourceManager("Mis.Agent.Localization.Messages", 
                    AppDomain.CurrentDomain.GetAssemblies()
                        .FirstOrDefault(a => a.GetName().Name == "Mis.Agent"));
                
                if (mainResourceManager != null)
                {
                    _instance.RegisterResourceManager("Main", mainResourceManager);
                }

                // Try to register plugin resources
                RegisterPluginResources();
            }
            catch (Exception ex)
            {
                // Log error but don't fail initialization
                Console.WriteLine($"Error initializing localization resources: {ex.Message}");
            }
        }

        private static void RegisterPluginResources()
        {
            try
            {
                // Register Barcode plugin resources
                var barcodeAssembly = AppDomain.CurrentDomain.GetAssemblies()
                    .FirstOrDefault(a => a.GetName().Name == "Mis.Agent.Barcode");
                if (barcodeAssembly != null)
                {
                    var barcodeResourceManager = new ResourceManager("Mis.Agent.Barcode.Resources.BarcodeResources", barcodeAssembly);
                    _instance.RegisterResourceManager("Barcode", barcodeResourceManager);
                }

                // Register Print plugin resources
                var printAssembly = AppDomain.CurrentDomain.GetAssemblies()
                    .FirstOrDefault(a => a.GetName().Name == "Mis.Agent.Print");
                if (printAssembly != null)
                {
                    var printResourceManager = new ResourceManager("Mis.Agent.Print.Resources.PrintResources", printAssembly);
                    _instance.RegisterResourceManager("Print", printResourceManager);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering plugin resources: {ex.Message}");
            }
        }

        public static void SetCulture(string cultureName)
        {
            Instance.SetCulture(cultureName);
        }

        public static string GetString(string key, string defaultValue = null)
        {
            return Instance.GetString(key, defaultValue);
        }

        public static bool IsRightToLeft => Instance.IsRightToLeft;

        public static CultureInfo CurrentCulture => Instance.CurrentCulture;

        public static event EventHandler<CultureChangedEventArgs> CultureChanged
        {
            add { Instance.CultureChanged += value; }
            remove { Instance.CultureChanged -= value; }
        }
    }
}
