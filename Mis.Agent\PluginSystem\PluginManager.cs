using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;

namespace Mis.Agent.PluginSystem
{
    /// <summary>
    /// Manages plugin discovery, loading, and lifecycle
    /// </summary>
    public class PluginManager
    {
        private static PluginManager? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of the plugin manager
        /// </summary>
        public static PluginManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new PluginManager();
                    }
                }
                return _instance;
            }
        }

        private readonly List<PluginMetadata> _plugins = new();
        private readonly Dictionary<string, PluginMetadata> _pluginsByName = new();
        private IServiceProvider? _serviceProvider;

        /// <summary>
        /// All discovered plugins
        /// </summary>
        public IReadOnlyList<PluginMetadata> Plugins => _plugins.AsReadOnly();

        /// <summary>
        /// All loaded plugins
        /// </summary>
        public IReadOnlyList<PluginMetadata> LoadedPlugins => _plugins.Where(p => p.IsLoaded).ToList().AsReadOnly();

        /// <summary>
        /// Event fired when a plugin is loaded
        /// </summary>
        public event EventHandler<PluginLoadedEventArgs>? PluginLoaded;

        /// <summary>
        /// Event fired when a plugin fails to load
        /// </summary>
        public event EventHandler<PluginLoadFailedEventArgs>? PluginLoadFailed;

        private PluginManager()
        {
        }

        /// <summary>
        /// Sets the service provider for dependency injection
        /// </summary>
        public void SetServiceProvider(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Discovers plugins in the specified directory
        /// </summary>
        public void DiscoverPlugins(string pluginDirectory)
        {
            if (!Directory.Exists(pluginDirectory))
            {
                Console.WriteLine($"Plugin directory does not exist: {pluginDirectory}");
                return;
            }

            var dllFiles = Directory.GetFiles(pluginDirectory, "*.dll", SearchOption.AllDirectories);

            foreach (var dllFile in dllFiles)
            {
                try
                {
                    DiscoverPluginsInAssembly(dllFile);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error discovering plugins in {dllFile}: {ex.Message}");
                }
            }

            Console.WriteLine($"Discovered {_plugins.Count} plugins in {pluginDirectory}");
        }

        /// <summary>
        /// Discovers plugins in a specific assembly file
        /// </summary>
        private void DiscoverPluginsInAssembly(string assemblyPath)
        {
            try
            {
                // Check if assembly is already loaded to avoid duplicates
                var assemblyName = AssemblyName.GetAssemblyName(assemblyPath);
                var loadedAssembly = AppDomain.CurrentDomain.GetAssemblies()
                    .FirstOrDefault(a => a.GetName().Name == assemblyName.Name);

                Assembly assembly;
                if (loadedAssembly != null)
                {
                    Console.WriteLine($"Assembly {assemblyName.Name} already loaded, using existing instance");
                    assembly = loadedAssembly;
                }
                else
                {
                    assembly = Assembly.LoadFile(assemblyPath);
                    Console.WriteLine($"Loaded assembly: {assembly.FullName}");
                }

                var types = GetTypesFromAssembly(assembly);

                foreach (var type in types)
                {
                    var pluginAttribute = type.GetCustomAttributes(false)
                        .FirstOrDefault(attr => attr.GetType().Name == "PluginAttribute");
                    if (pluginAttribute == null || type.IsAbstract || type.IsInterface)
                        continue;

                    var metadata = CreatePluginMetadata(type, assembly, assemblyPath, pluginAttribute);
                    if (metadata != null)
                    {
                        _plugins.Add(metadata);
                        _pluginsByName[metadata.Name] = metadata;
                        Console.WriteLine($"Discovered plugin: {metadata.Name} v{metadata.Version}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading assembly {assemblyPath}: {ex.Message}");
            }
        }

        /// <summary>
        /// Safely gets types from an assembly, handling ReflectionTypeLoadException
        /// </summary>
        private Type[] GetTypesFromAssembly(Assembly assembly)
        {
            try
            {
                return assembly.GetTypes();
            }
            catch (ReflectionTypeLoadException ex)
            {
                // Return only the types that loaded successfully
                return ex.Types.Where(t => t != null).ToArray()!;
            }
        }

        /// <summary>
        /// Creates plugin metadata from a type
        /// </summary>
        private PluginMetadata? CreatePluginMetadata(Type type, Assembly assembly, string assemblyPath, object pluginAttribute)
        {
            try
            {
                var attrType = pluginAttribute.GetType();
                var metadata = new PluginMetadata
                {
                    Name = (string)attrType.GetProperty("Name")?.GetValue(pluginAttribute) ?? type.Name,
                    Version = (string)attrType.GetProperty("Version")?.GetValue(pluginAttribute) ?? "1.0.0",
                    Description = (string)attrType.GetProperty("Description")?.GetValue(pluginAttribute) ?? "",
                    Order = (int)(attrType.GetProperty("Order")?.GetValue(pluginAttribute) ?? 0),
                    AutoLoad = (bool)(attrType.GetProperty("AutoLoad")?.GetValue(pluginAttribute) ?? true),
                    PluginType = type,
                    Assembly = assembly,
                    AssemblyPath = assemblyPath
                };

                // Find plugin methods
                var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance);

                foreach (var method in methods)
                {
                    var methodAttrs = method.GetCustomAttributes(false);

                    if (methodAttrs.Any(attr => attr.GetType().Name == "PluginTabProviderAttribute"))
                        metadata.TabProviderMethod = method;

                    if (methodAttrs.Any(attr => attr.GetType().Name == "PluginInitializeAttribute"))
                        metadata.InitializeMethod = method;

                    if (methodAttrs.Any(attr => attr.GetType().Name == "PluginCleanupAttribute"))
                        metadata.CleanupMethod = method;

                    if (methodAttrs.Any(attr => attr.GetType().Name == "PluginNotificationHandlerAttribute"))
                        metadata.NotificationHandlerMethod = method;

                    var configAttr = methodAttrs.FirstOrDefault(attr => attr.GetType().Name == "PluginConfigurationAttribute");
                    if (configAttr != null)
                    {
                        var key = (string)configAttr.GetType().GetProperty("Key")?.GetValue(configAttr) ?? method.Name;
                        metadata.ConfigurationMethods[key] = method;
                    }
                }

                return metadata;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating metadata for plugin type {type.FullName}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Loads all auto-load plugins
        /// </summary>
        public void LoadAutoLoadPlugins()
        {
            var autoLoadPlugins = _plugins.Where(p => p.AutoLoad && !p.IsLoaded).OrderBy(p => p.Order);

            foreach (var plugin in autoLoadPlugins)
            {
                LoadPlugin(plugin);
            }
        }

        /// <summary>
        /// Loads a specific plugin
        /// </summary>
        public bool LoadPlugin(PluginMetadata plugin)
        {
            if (plugin.IsLoaded)
                return true;

            try
            {
                // Create plugin instance
                if (_serviceProvider != null)
                {
                    plugin.PluginInstance = ActivatorUtilities.CreateInstance(_serviceProvider, plugin.PluginType);
                }
                else
                {
                    plugin.PluginInstance = Activator.CreateInstance(plugin.PluginType);
                }

                if (plugin.PluginInstance == null)
                {
                    throw new InvalidOperationException($"Failed to create instance of plugin {plugin.Name}");
                }

                // Initialize the plugin
                if (!plugin.Initialize())
                {
                    return false;
                }

                plugin.IsLoaded = true;
                plugin.LoadedAt = DateTime.Now;

                PluginLoaded?.Invoke(this, new PluginLoadedEventArgs(plugin));
                Console.WriteLine($"Loaded plugin: {plugin.Name} v{plugin.Version}");
                return true;
            }
            catch (Exception ex)
            {
                plugin.LoadError = ex;
                PluginLoadFailed?.Invoke(this, new PluginLoadFailedEventArgs(plugin, ex));
                Console.WriteLine($"Failed to load plugin {plugin.Name}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads a plugin by name
        /// </summary>
        public bool LoadPlugin(string pluginName)
        {
            if (_pluginsByName.TryGetValue(pluginName, out var plugin))
            {
                return LoadPlugin(plugin);
            }
            return false;
        }

        /// <summary>
        /// Unloads a plugin
        /// </summary>
        public void UnloadPlugin(PluginMetadata plugin)
        {
            if (!plugin.IsLoaded)
                return;

            try
            {
                plugin.Cleanup();
                plugin.PluginInstance = null;
                plugin.IsLoaded = false;
                plugin.TabPage = null;
                Console.WriteLine($"Unloaded plugin: {plugin.Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error unloading plugin {plugin.Name}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets a plugin by name
        /// </summary>
        public PluginMetadata? GetPlugin(string name)
        {
            _pluginsByName.TryGetValue(name, out var plugin);
            return plugin;
        }

        /// <summary>
        /// Gets all tab pages from loaded plugins
        /// </summary>
        public List<TabPage> GetTabPages()
        {
            var tabPages = new List<TabPage>();

            var orderedPlugins = LoadedPlugins.OrderBy(p => p.Order);

            foreach (var plugin in orderedPlugins)
            {
                try
                {
                    var tabPage = plugin.GetTabPage();
                    Console.WriteLine($"tabPage: {tabPage}");

                    if (tabPage != null)
                    {
                        tabPages.Add(tabPage);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting tab page from plugin {plugin.Name}: {ex.Message}");
                }
            }

            return tabPages;
        }

        /// <summary>
        /// Shows a notification using all loaded plugins that support notifications
        /// </summary>
        public void ShowNotification(string title, string text)
        {
            foreach (var plugin in LoadedPlugins)
            {
                plugin.ShowNotification(title, text);
            }
        }

        /// <summary>
        /// Gets a configuration value from a specific plugin
        /// </summary>
        public object? GetConfiguration(string pluginName, string key)
        {
            var plugin = GetPlugin(pluginName);
            return plugin?.GetConfiguration(key);
        }

        /// <summary>
        /// Unloads all plugins
        /// </summary>
        public void UnloadAllPlugins()
        {
            foreach (var plugin in LoadedPlugins.ToList())
            {
                UnloadPlugin(plugin);
            }
        }
    }

    /// <summary>
    /// Event args for plugin loaded event
    /// </summary>
    public class PluginLoadedEventArgs : EventArgs
    {
        public PluginMetadata Plugin { get; }

        public PluginLoadedEventArgs(PluginMetadata plugin)
        {
            Plugin = plugin;
        }
    }

    /// <summary>
    /// Event args for plugin load failed event
    /// </summary>
    public class PluginLoadFailedEventArgs : EventArgs
    {
        public PluginMetadata Plugin { get; }
        public Exception Exception { get; }

        public PluginLoadFailedEventArgs(PluginMetadata plugin, Exception exception)
        {
            Plugin = plugin;
            Exception = exception;
        }
    }
}
