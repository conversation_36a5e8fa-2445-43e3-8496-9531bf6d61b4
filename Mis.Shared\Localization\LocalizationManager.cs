using System.Globalization;
using System.Resources;
using System.Reflection;
using Microsoft.Extensions.Configuration;

namespace Mis.Shared.Localization
{
    public class LocalizationManager : ILocalizationManager
    {
        private readonly Dictionary<string, ResourceManager> _resourceManagers;
        private CultureInfo _currentCulture;
        private readonly IConfiguration _configuration;
        private readonly string _settingsPath;

        public event EventHandler<CultureChangedEventArgs> CultureChanged;

        public CultureInfo CurrentCulture => _currentCulture;

        public bool IsRightToLeft => _currentCulture.TextInfo.IsRightToLeft;

        public LocalizationManager(IConfiguration configuration = null)
        {
            _configuration = configuration;
            _resourceManagers = new Dictionary<string, ResourceManager>();
            _settingsPath = Path.Combine(AppContext.BaseDirectory, "language.settings");
            
            // Initialize with system culture or load saved preference
            LoadLanguagePreference();
        }

        public void RegisterResourceManager(string key, ResourceManager resourceManager)
        {
            _resourceManagers[key] = resourceManager;
        }

        public void SetCulture(string cultureName)
        {
            var oldCulture = _currentCulture;
            var newCulture = new CultureInfo(cultureName);
            
            _currentCulture = newCulture;
            Thread.CurrentThread.CurrentCulture = newCulture;
            Thread.CurrentThread.CurrentUICulture = newCulture;
            CultureInfo.DefaultThreadCurrentCulture = newCulture;
            CultureInfo.DefaultThreadCurrentUICulture = newCulture;

            // Fire culture changed event
            CultureChanged?.Invoke(this, new CultureChangedEventArgs
            {
                OldCulture = oldCulture,
                NewCulture = newCulture,
                IsRightToLeft = IsRightToLeft
            });

            SaveLanguagePreference();
        }

        public string GetString(string key, string defaultValue = null)
        {
            // Try to get from all registered resource managers
            foreach (var resourceManager in _resourceManagers.Values)
            {
                try
                {
                    var value = resourceManager.GetString(key, _currentCulture);
                    if (!string.IsNullOrEmpty(value))
                        return value;
                }
                catch
                {
                    // Continue to next resource manager
                }
            }

            return defaultValue ?? key;
        }

        public string GetString(string key, params object[] args)
        {
            var format = GetString(key);
            try
            {
                return string.Format(format, args);
            }
            catch
            {
                return format;
            }
        }

        public void SaveLanguagePreference()
        {
            try
            {
                File.WriteAllText(_settingsPath, _currentCulture.Name);
            }
            catch
            {
                // Ignore save errors
            }
        }

        public void LoadLanguagePreference()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var cultureName = File.ReadAllText(_settingsPath).Trim();
                    if (!string.IsNullOrEmpty(cultureName))
                    {
                        _currentCulture = new CultureInfo(cultureName);
                        Thread.CurrentThread.CurrentCulture = _currentCulture;
                        Thread.CurrentThread.CurrentUICulture = _currentCulture;
                        CultureInfo.DefaultThreadCurrentCulture = _currentCulture;
                        CultureInfo.DefaultThreadCurrentUICulture = _currentCulture;
                        return;
                    }
                }
            }
            catch
            {
                // Ignore load errors
            }

            // Default to English if no preference found
            _currentCulture = new CultureInfo("en-US");
            Thread.CurrentThread.CurrentCulture = _currentCulture;
            Thread.CurrentThread.CurrentUICulture = _currentCulture;
            CultureInfo.DefaultThreadCurrentCulture = _currentCulture;
            CultureInfo.DefaultThreadCurrentUICulture = _currentCulture;
        }
    }
}
