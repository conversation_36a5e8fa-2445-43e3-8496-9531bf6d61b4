using System;
using System.Collections.Generic;
using System.Reflection;

namespace Mis.Agent.Barcode
{
    /// <summary>
    /// Helper class for plugins to access main application services via reflection
    /// This eliminates the need for assembly references between plugins and the main application
    /// Copy this class to your plugin project to access SerialPortManager functionality
    /// </summary>
    public static class PluginServiceHelper
    {
        private static Assembly? _mainAssembly;
        private static Type? _pluginManagerType;
        private static Type? _pluginServiceAccessType;

        /// <summary>
        /// Initialize the helper by finding the main application assembly
        /// </summary>
        static PluginServiceHelper()
        {
            try
            {
                // Find the main application assembly
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                foreach (var assembly in assemblies)
                {
                    if (assembly.GetName().Name == "Mis.Agent")
                    {
                        _mainAssembly = assembly;
                        break;
                    }
                }

                if (_mainAssembly != null)
                {
                    _pluginManagerType = _mainAssembly.GetType("Mis.Agent.PluginSystem.PluginManager");
                    _pluginServiceAccessType = _mainAssembly.GetType("Mis.Agent.PluginSystem.PluginManager+PluginServiceAccess");

                    if (_pluginServiceAccessType != null)
                    {
                        Console.WriteLine("PluginServiceHelper initialized successfully");
                    }
                    else
                    {
                        Console.WriteLine("Warning: PluginServiceAccess type not found");
                    }
                }
                else
                {
                    Console.WriteLine("Warning: Main application assembly not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing PluginServiceHelper: {ex.Message}");
            }
        }

        /// <summary>
        /// Initialize barcode functionality
        /// </summary>
        public static void PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("PublicInitialize",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { barcodeUrl, comPort, isCaptureImageMode });
                    Console.WriteLine($"PublicInitialize called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("PublicInitialize method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling PublicInitialize: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start listening on COM port
        /// </summary>
        public static void StartListening(string comPort)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("StartListening",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { comPort });
                    Console.WriteLine($"StartListening called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("StartListening method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling StartListening: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Scan with selected scanner
        /// </summary>
        public static string ScanWithSelectedScanner(string scannerName)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("ScanWithSelectedScanner",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    var result = method.Invoke(null, new object[] { scannerName });
                    Console.WriteLine($"ScanWithSelectedScanner called successfully via reflection");
                    return result?.ToString() ?? string.Empty;
                }
                else
                {
                    throw new MethodAccessException("ScanWithSelectedScanner method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling ScanWithSelectedScanner: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get available COM ports
        /// </summary>
        public static string[] GetAvailableCOMPorts()
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    Console.WriteLine("PluginServiceHelper not properly initialized, returning empty array");
                    return new string[0];
                }

                var method = _pluginServiceAccessType.GetMethod("GetAvailableCOMPorts",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    var result = method.Invoke(null, null);
                    return result as string[] ?? new string[0];
                }
                else
                {
                    Console.WriteLine("GetAvailableCOMPorts method not found");
                    return new string[0];
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableCOMPorts: {ex.Message}");
                return new string[0];
            }
        }

        /// <summary>
        /// Get available scanners
        /// </summary>
        public static List<string> GetAvailableScanners()
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    Console.WriteLine("PluginServiceHelper not properly initialized, returning empty list");
                    return new List<string>();
                }

                var method = _pluginServiceAccessType.GetMethod("GetAvailableScanners",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    var result = method.Invoke(null, null);
                    return result as List<string> ?? new List<string>();
                }
                else
                {
                    Console.WriteLine("GetAvailableScanners method not found");
                    return new List<string>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableScanners: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Check if port is open
        /// </summary>
        public static bool IsPortOpen
        {
            get
            {
                try
                {
                    if (_pluginServiceAccessType == null)
                    {
                        return false;
                    }

                    var property = _pluginServiceAccessType.GetProperty("IsPortOpen",
                        BindingFlags.Public | BindingFlags.Static);

                    if (property != null)
                    {
                        var result = property.GetValue(null);
                        return result is bool boolResult && boolResult;
                    }
                    else
                    {
                        Console.WriteLine("IsPortOpen property not found");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting IsPortOpen: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Show notification
        /// </summary>
        public static void ShowNotification(string title, string text)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    Console.WriteLine($"Plugin Notification: {title} - {text}");
                    return;
                }

                var method = _pluginServiceAccessType.GetMethod("ShowNotification",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { title, text });
                }
                else
                {
                    Console.WriteLine($"Plugin Notification: {title} - {text}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error showing notification: {ex.Message}");
                Console.WriteLine($"Fallback Notification: {title} - {text}");
            }
        }

        /// <summary>
        /// Initialize hub connection
        /// </summary>
        public static void InitializeHubConnection(string hubUrl)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("InitializeHubConnection",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { hubUrl });
                    Console.WriteLine($"InitializeHubConnection called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("InitializeHubConnection method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling InitializeHubConnection: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Capture image asynchronously
        /// </summary>
        public static void CaptureImageAsync(string comPort)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("CaptureImageAsync",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { comPort });
                    Console.WriteLine($"CaptureImageAsync called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("CaptureImageAsync method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling CaptureImageAsync: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Write command to serial port
        /// </summary>
        public static void Write(string command)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("Write",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { command });
                    Console.WriteLine($"Write called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("Write method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling Write: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Force release port
        /// </summary>
        public static void ForceReleasePort()
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("ForceReleasePort",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, null);
                    Console.WriteLine($"ForceReleasePort called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("ForceReleasePort method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling ForceReleasePort: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop listening
        /// </summary>
        public static void StopListening()
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("StopListening",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, null);
                    Console.WriteLine($"StopListening called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("StopListening method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling StopListening: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get or set capture image mode
        /// </summary>
        public static bool IsCaptureImageMode
        {
            get
            {
                try
                {
                    if (_pluginServiceAccessType == null)
                    {
                        return false;
                    }

                    var property = _pluginServiceAccessType.GetProperty("IsCaptureImageMode",
                        BindingFlags.Public | BindingFlags.Static);

                    if (property != null)
                    {
                        var result = property.GetValue(null);
                        return result is bool boolResult && boolResult;
                    }
                    else
                    {
                        Console.WriteLine("IsCaptureImageMode property not found");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting IsCaptureImageMode: {ex.Message}");
                    return false;
                }
            }
            set
            {
                try
                {
                    if (_pluginServiceAccessType == null)
                    {
                        return;
                    }

                    var property = _pluginServiceAccessType.GetProperty("IsCaptureImageMode",
                        BindingFlags.Public | BindingFlags.Static);

                    if (property != null)
                    {
                        property.SetValue(null, value);
                        Console.WriteLine($"IsCaptureImageMode set to {value} via reflection");
                    }
                    else
                    {
                        Console.WriteLine("IsCaptureImageMode property not found");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting IsCaptureImageMode: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get or set last scanned base64 data
        /// </summary>
        public static string LastScannedBase64Data
        {
            get
            {
                try
                {
                    if (_pluginServiceAccessType == null)
                    {
                        return string.Empty;
                    }

                    var property = _pluginServiceAccessType.GetProperty("LastScannedBase64Data",
                        BindingFlags.Public | BindingFlags.Static);

                    if (property != null)
                    {
                        var result = property.GetValue(null);
                        return result?.ToString() ?? string.Empty;
                    }
                    else
                    {
                        Console.WriteLine("LastScannedBase64Data property not found");
                        return string.Empty;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting LastScannedBase64Data: {ex.Message}");
                    return string.Empty;
                }
            }
            set
            {
                try
                {
                    if (_pluginServiceAccessType == null)
                    {
                        return;
                    }

                    var property = _pluginServiceAccessType.GetProperty("LastScannedBase64Data",
                        BindingFlags.Public | BindingFlags.Static);

                    if (property != null)
                    {
                        property.SetValue(null, value);
                        Console.WriteLine($"LastScannedBase64Data set via reflection");
                    }
                    else
                    {
                        Console.WriteLine("LastScannedBase64Data property not found");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting LastScannedBase64Data: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Subscribe to SerialPortManager events
        /// </summary>
        public static void SubscribeToEvent(string eventName, Delegate handler)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("SubscribeToEvent",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { eventName, handler });
                    Console.WriteLine($"SubscribeToEvent called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("SubscribeToEvent method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling SubscribeToEvent: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Unsubscribe from SerialPortManager events
        /// </summary>
        public static void UnsubscribeFromEvent(string eventName, Delegate handler)
        {
            try
            {
                if (_pluginServiceAccessType == null)
                {
                    throw new InvalidOperationException("PluginServiceHelper not properly initialized");
                }

                var method = _pluginServiceAccessType.GetMethod("UnsubscribeFromEvent",
                    BindingFlags.Public | BindingFlags.Static);

                if (method != null)
                {
                    method.Invoke(null, new object[] { eventName, handler });
                    Console.WriteLine($"UnsubscribeFromEvent called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("UnsubscribeFromEvent method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling UnsubscribeFromEvent: {ex.Message}");
                throw;
            }
        }
    }
}
