using System.ComponentModel;
using System.Globalization;
using System.Windows.Forms;

namespace Mis.Shared.Localization
{
    public class LocalizableForm : Form
    {
        protected ILocalizationManager _localizationManager;
        private bool _isRtlApplied = false;

        public LocalizableForm()
        {
            // This will be injected or set by derived classes
        }

        public LocalizableForm(ILocalizationManager localizationManager)
        {
            _localizationManager = localizationManager;
            if (_localizationManager != null)
            {
                _localizationManager.CultureChanged += OnCultureChanged;
            }
        }

        protected virtual void OnCultureChanged(object sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
            ApplyRtlLayout(e.IsRightToLeft);
        }

        protected virtual void ApplyLocalization()
        {
            // Override in derived classes to apply specific localizations
        }

        protected virtual void ApplyRtlLayout(bool isRtl)
        {
            if (isRtl && !_isRtlApplied)
            {
                RightToLeft = RightToLeft.Yes;
                RightToLeftLayout = true;
                _isRtlApplied = true;
            }
            else if (!isRtl && _isRtlApplied)
            {
                RightToLeft = RightToLeft.No;
                RightToLeftLayout = false;
                _isRtlApplied = false;
            }

            // Apply RTL to all child controls
            ApplyRtlToControls(this.Controls, isRtl);
        }

        private void ApplyRtlToControls(Control.ControlCollection controls, bool isRtl)
        {
            foreach (Control control in controls)
            {
                if (control is TabControl tabControl)
                {
                    tabControl.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                    tabControl.RightToLeftLayout = isRtl;
                    
                    foreach (TabPage tabPage in tabControl.TabPages)
                    {
                        tabPage.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                        ApplyRtlToControls(tabPage.Controls, isRtl);
                    }
                }
                else
                {
                    control.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                }

                if (control.HasChildren)
                {
                    ApplyRtlToControls(control.Controls, isRtl);
                }
            }
        }

        protected string GetLocalizedString(string key, string defaultValue = null)
        {
            return _localizationManager?.GetString(key, defaultValue) ?? defaultValue ?? key;
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            if (_localizationManager != null)
            {
                ApplyLocalization();
                ApplyRtlLayout(_localizationManager.IsRightToLeft);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && _localizationManager != null)
            {
                _localizationManager.CultureChanged -= OnCultureChanged;
            }
            base.Dispose(disposing);
        }
    }
}
