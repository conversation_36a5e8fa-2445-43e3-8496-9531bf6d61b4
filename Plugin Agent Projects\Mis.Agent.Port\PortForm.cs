
using System.Diagnostics;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Mis.Agent.Port
{
    public partial class PortForm : Form
    {
        private IPortAppService _portAppService;
        public PortForm(IPortAppService portAppService)
        {
            _portAppService = portAppService;
            InitializeComponent();
            InitializeLocalization();
            textBox3.Text = _portAppService.GetBaseUrl();
        }

        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object? sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("PortFormTitle", "Port Configuration");

            // Update tab text - ensure it's never null, keep original if localization fails
            var originalText = PortTap.Text; // Keep the designer value as fallback
            var tabText = SimpleLocalization.GetString("PortTabText", "Port Settings");
            PortTap.Text = string.IsNullOrEmpty(tabText) ? (string.IsNullOrEmpty(originalText) ? "Port Settings" : originalText) : tabText;

            // Update labels
            label3.Text = SimpleLocalization.GetString("BaseUrlLabel", "Base URL:");


        }


        private void RestartApplication()
        {
            try
            {

                // Close the current application
                Application.Exit();

                // Start a new instance of the application
                Process.Start(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                _portAppService.ShowNotification($"Failed to restart the application: {ex.Message}", "Error");
                // Optionally log the exception
            }
        }

    }




}
