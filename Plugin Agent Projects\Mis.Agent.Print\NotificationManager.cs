using System;

namespace Mis.Agent.Print
{
    public delegate void NotificationEventHandler(object sender, NotificationEventArgs e);

    public class NotificationEventArgs : EventArgs
    {
        public bool IsEnabled { get; set; }
    }

    public static class NotificationManager
    {
        public static event NotificationEventHandler NotificationEvent;

        public static void Notify(NotificationEventArgs e)
        {
            NotificationEvent?.Invoke(null, e);
        }
    }
}
