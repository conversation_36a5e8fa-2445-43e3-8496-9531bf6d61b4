
using System.Diagnostics;

namespace Mis.Agent.Barcode
{
    public partial class BarcodeForm : Form
    {
        private readonly IBarcodeAppService _barcodeAppService;

        public BarcodeForm(IBarcodeAppService barcodeAppService)
        {
            _barcodeAppService = barcodeAppService;
            InitializeComponent();
            InitializeLocalization();
        }

        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object? sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("BarcodeFormTitle", "Barcode Configuration");

            // Update tab text - ensure it's never null, keep original if localization fails
            var originalText = BarcodeTab.Text; // Keep the designer value as fallback
            //var tabText = SimpleLocalization.GetString("BarcodeTabText", "Barcode Settings");
            //BarcodeTab.Text = string.IsNullOrEmpty(tabText) ? (string.IsNullOrEmpty(originalText) ? "Barcode Settings" : originalText) : tabText;

            // Update labels
            label4.Text = SimpleLocalization.GetString("BarcodeUrlLabel", "Barcode URL:");
            label5.Text = SimpleLocalization.GetString("ComPortLabel", "COM Port:");
            label6.Text = SimpleLocalization.GetString("AvailablePortsLabel", "Available Ports:");

            // Update buttons
            button3.Text = SimpleLocalization.GetString("TestConnectionButton", "Test Connection");
        }

        private void RestartApplication()
        {
            try
            {

                // Close the current application
                Application.Exit();

                // Start a new instance of the application
                Process.Start(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to restart the application: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Optionally log the exception
            }
        }

    }
}
