using System;
using System.Collections.Generic;
using System.Reflection;

namespace Mis.Agent.Barcode
{
    /// <summary>
    /// Reflection helper for accessing main application services from external applications
    /// This allows external apps (like barcode controllers) to access SerialPortManager functionality
    /// </summary>
    public static class ReflectionHelper
    {
        private static Assembly? _mainAssembly;
        private static Type? _serialPortManagerType;
        private static object? _serialPortManagerInstance;

        /// <summary>
        /// Initialize the helper by finding the main application assembly and SerialPortManager
        /// </summary>
        public static bool Initialize()
        {
            try
            {
                // Find the main application assembly
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                foreach (var assembly in assemblies)
                {
                    if (assembly.GetName().Name == "Mis.Agent")
                    {
                        _mainAssembly = assembly;
                        break;
                    }
                }

                if (_mainAssembly != null)
                {
                    _serialPortManagerType = _mainAssembly.GetType("Mis.Agent.Services.SerialPortManager");
                    
                    if (_serialPortManagerType != null)
                    {
                        // Get the singleton instance
                        var instanceProperty = _serialPortManagerType.GetProperty("Instance", 
                            BindingFlags.Public | BindingFlags.Static);
                        
                        if (instanceProperty != null)
                        {
                            _serialPortManagerInstance = instanceProperty.GetValue(null);
                            Console.WriteLine("ReflectionHelper initialized successfully");
                            return true;
                        }
                    }
                }

                Console.WriteLine("Warning: Could not initialize ReflectionHelper - main assembly or SerialPortManager not found");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing ReflectionHelper: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Initialize barcode functionality
        /// </summary>
        public static void PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            try
            {
                if (_serialPortManagerType == null || _serialPortManagerInstance == null)
                {
                    if (!Initialize())
                    {
                        throw new InvalidOperationException("ReflectionHelper could not be initialized");
                    }
                }

                var method = _serialPortManagerType.GetMethod("PublicInitialize", 
                    new Type[] { typeof(string), typeof(string), typeof(bool) });
                
                if (method != null)
                {
                    method.Invoke(_serialPortManagerInstance, new object[] { barcodeUrl, comPort, isCaptureImageMode });
                    Console.WriteLine($"PublicInitialize called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("PublicInitialize method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling PublicInitialize: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start listening on COM port
        /// </summary>
        public static void StartListening(string comPort)
        {
            try
            {
                if (_serialPortManagerType == null || _serialPortManagerInstance == null)
                {
                    if (!Initialize())
                    {
                        throw new InvalidOperationException("ReflectionHelper could not be initialized");
                    }
                }

                var method = _serialPortManagerType.GetMethod("StartListening", 
                    new Type[] { typeof(string) });
                
                if (method != null)
                {
                    method.Invoke(_serialPortManagerInstance, new object[] { comPort });
                    Console.WriteLine($"StartListening called successfully via reflection");
                }
                else
                {
                    throw new MethodAccessException("StartListening method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling StartListening: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Scan with selected scanner
        /// </summary>
        public static string ScanWithSelectedScanner(string scannerName)
        {
            try
            {
                if (_serialPortManagerType == null || _serialPortManagerInstance == null)
                {
                    if (!Initialize())
                    {
                        throw new InvalidOperationException("ReflectionHelper could not be initialized");
                    }
                }

                var method = _serialPortManagerType.GetMethod("ScanWithSelectedScanner", 
                    new Type[] { typeof(string) });
                
                if (method != null)
                {
                    var result = method.Invoke(_serialPortManagerInstance, new object[] { scannerName });
                    Console.WriteLine($"ScanWithSelectedScanner called successfully via reflection");
                    return result?.ToString() ?? string.Empty;
                }
                else
                {
                    throw new MethodAccessException("ScanWithSelectedScanner method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling ScanWithSelectedScanner: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get available COM ports
        /// </summary>
        public static string[] GetAvailableCOMPorts()
        {
            try
            {
                if (_serialPortManagerType == null || _serialPortManagerInstance == null)
                {
                    if (!Initialize())
                    {
                        Console.WriteLine("ReflectionHelper could not be initialized, returning empty array");
                        return new string[0];
                    }
                }

                var method = _serialPortManagerType.GetMethod("GetAvailableCOMPorts", 
                    Type.EmptyTypes);
                
                if (method != null)
                {
                    var result = method.Invoke(_serialPortManagerInstance, null);
                    return result as string[] ?? new string[0];
                }
                else
                {
                    Console.WriteLine("GetAvailableCOMPorts method not found");
                    return new string[0];
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling GetAvailableCOMPorts: {ex.Message}");
                return new string[0];
            }
        }

        /// <summary>
        /// Check if port is open
        /// </summary>
        public static bool IsPortOpen
        {
            get
            {
                try
                {
                    if (_serialPortManagerType == null || _serialPortManagerInstance == null)
                    {
                        if (!Initialize())
                        {
                            return false;
                        }
                    }

                    var property = _serialPortManagerType.GetProperty("IsPortOpen");
                    
                    if (property != null)
                    {
                        var result = property.GetValue(_serialPortManagerInstance);
                        return result is bool boolResult && boolResult;
                    }
                    else
                    {
                        Console.WriteLine("IsPortOpen property not found");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting IsPortOpen: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Capture image using the barcode scanner
        /// </summary>
        public static void CaptureImage(string comPort)
        {
            try
            {
                if (_serialPortManagerType == null || _serialPortManagerInstance == null)
                {
                    if (!Initialize())
                    {
                        throw new InvalidOperationException("ReflectionHelper could not be initialized");
                    }
                }

                var method = _serialPortManagerType.GetMethod("CaptureImageAsync", 
                    new Type[] { typeof(string) });
                
                if (method != null)
                {
                    method.Invoke(_serialPortManagerInstance, new object[] { comPort });
                    Console.WriteLine($"CaptureImageAsync called successfully via reflection");
                }
                else
                {
                    Console.WriteLine("CaptureImageAsync method not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling CaptureImageAsync: {ex.Message}");
                throw;
            }
        }
    }
}
