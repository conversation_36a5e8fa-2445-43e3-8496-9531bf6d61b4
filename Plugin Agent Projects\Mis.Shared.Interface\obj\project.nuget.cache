{"version": 2, "dgSpecHash": "kL4fiKlMzno=", "success": true, "projectFilePath": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Shared.Interface\\Mis.Shared.Interface.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\entityframework\\6.4.4\\entityframework.6.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.connections.abstractions\\9.0.6\\microsoft.aspnetcore.connections.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.connections.client\\9.0.6\\microsoft.aspnetcore.http.connections.client.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.connections.common\\9.0.6\\microsoft.aspnetcore.http.connections.common.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.client\\9.0.6\\microsoft.aspnetcore.signalr.client.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.client.core\\9.0.6\\microsoft.aspnetcore.signalr.client.core.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.common\\9.0.6\\microsoft.aspnetcore.signalr.common.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.protocols.json\\9.0.6\\microsoft.aspnetcore.signalr.protocols.json.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.6\\microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.6\\microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.6\\microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.features\\9.0.6\\microsoft.extensions.features.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.6\\microsoft.extensions.logging.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.6\\microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.6\\microsoft.extensions.options.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.6\\microsoft.extensions.primitives.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\9.0.6\\microsoft.win32.systemevents.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm.runtime.native.system.io.ports\\9.0.6\\runtime.android-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.android-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x64.runtime.native.system.io.ports\\9.0.6\\runtime.android-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x86.runtime.native.system.io.ports\\9.0.6\\runtime.android-x86.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm.runtime.native.system.io.ports\\9.0.6\\runtime.linux-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-x64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm.runtime.native.system.io.ports\\9.0.6\\runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-x64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-x64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-x64.runtime.native.system.io.ports\\9.0.6\\runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.ports\\9.0.6\\runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.osx-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-x64.runtime.native.system.io.ports\\9.0.6\\runtime.osx-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stub.system.data.sqlite.core.netstandard\\1.0.119\\stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\4.7.0\\system.codedom.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\4.7.0\\system.configuration.configurationmanager.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.1\\system.data.sqlclient.4.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite\\1.0.119\\system.data.sqlite.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.core\\1.0.119\\system.data.sqlite.core.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.ef6\\1.0.119\\system.data.sqlite.ef6.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\9.0.6\\system.drawing.common.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\9.0.6\\system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.serversentevents\\9.0.6\\system.net.serversentevents.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.7.0\\system.security.cryptography.protecteddata.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.7.0\\system.security.permissions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\9.0.6\\system.threading.channels.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\4.7.0\\system.windows.extensions.4.7.0.nupkg.sha512"], "logs": []}