{"format": 1, "restore": {"C:\\Mis Agent\\Plugin Agent Projects\\Mis.Agent.Barcode\\Mis.Agent.Barcode.csproj": {}}, "projects": {"C:\\Mis Agent\\Plugin Agent Projects\\Mis.Agent.Barcode\\Mis.Agent.Barcode.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Agent.Barcode\\Mis.Agent.Barcode.csproj", "projectName": "Mis.Agent.Barcode", "projectPath": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Agent.Barcode\\Mis.Agent.Barcode.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Agent.Barcode\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Owin": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.8.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.6, )"}, "System.Management": {"target": "Package", "version": "[9.0.6, )"}, "Volo.Abp.Core": {"target": "Package", "version": "[7.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}