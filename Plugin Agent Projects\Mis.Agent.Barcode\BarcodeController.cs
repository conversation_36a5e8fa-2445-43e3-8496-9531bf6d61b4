﻿
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    [ApiController]
    [Route("[controller]")]
    [EnableCors("AllowAll")]
    public class BarcodeController : ControllerBase
    {
        private readonly IBarcodeAppService _barcodeAppService;
        //private readonly IPublicBarcodeAppService _publicBarcodeAppService;
        private readonly IScannerAppService _scannerAppService;
        public BarcodeController(IBarcodeAppService barcodeAppService,
            IScannerAppService scannerAppService)
        {
            _barcodeAppService = barcodeAppService;
            _scannerAppService = scannerAppService;
        }

        // Your actions here...

        [HttpGet("ScanAsync")]
        public async Task<IActionResult> ScanAsync()
        {
            try
            {
                //Retrieve the value of IsScanByBarcodeReader from the configuration(appsettings.json)
                bool isScanByBarcodeReader = bool.Parse(_scannerAppService.GetSetting("IsScanByBarcodeReader"));
                if (isScanByBarcodeReader)
                {
                    //Barcode Reader logic
                    string comPort = _scannerAppService.GetCOMPort();
                    string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
                    // Initialize the barcode service
                    _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);
                    // Capture the image
                    // TODO: Implement image capture logic
                    Thread.Sleep(2000);

                    string scannedData = ""; // TODO: Get actual scanned data
                    if (string.IsNullOrEmpty(scannedData))
                    {
                        _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
                        return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
                    }

                    _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
                    return Ok(new { success = true, data = scannedData });
                }
                else
                {
                    //Non - barcode scanner logic: Get the scanner name from appsettings

                    string selectedScanner = _scannerAppService.GetSetting("Scanner");

                    if (string.IsNullOrEmpty(selectedScanner))
                    {
                        return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
                    }

                    //Scanner logic
                    var scanners = _scannerAppService.GetAvailableScanners();
                    if (scanners == null || !scanners.Any())
                    {
                        return BadRequest(new { success = false, message = "No scanners found." });
                    }

                    //Use the selected scanner to perform scanning
                    string scannedData = ""; // TODO: Implement scanner logic

                    if (string.IsNullOrEmpty(scannedData))
                    {
                        return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
                    }

                    return Ok(new { success = true, data = scannedData });
                }
                return Ok(new { success = true });
            }
            catch (UnauthorizedAccessException ex)
            {
                return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
            }
            catch (IOException ex)
            {
                return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }
    }
}
