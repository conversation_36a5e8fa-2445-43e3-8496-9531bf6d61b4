# SerialPortManager Reflection System

This document explains how to use the reflection-based system to access SerialPortManager functionality from plugins and external applications without assembly references.

## Overview

The reflection system allows plugins and external applications to access SerialPortManager methods without requiring direct assembly references. This is achieved through three main components:

1. **SerialPortBridge** - Direct bridge to SerialPortManager methods
2. **PluginReflectionHelper** - Helper for accessing main application services via reflection
3. **PluginServiceAccess** - Static class providing easy access for plugins

## Architecture

```
Plugin/External App
       ↓
PluginServiceHelper (in plugin)
       ↓ (reflection)
PluginServiceAccess (in main app)
       ↓
PluginReflectionHelper (in main app)
       ↓
SerialPortBridge (in main app)
       ↓
SerialPortManager (in main app)
```

## Usage in Plugins

### 1. Copy PluginServiceHelper to your plugin project

Copy the `PluginServiceHelper.cs` file to your plugin project. This provides reflection-based access to main application services.

### 2. Use in your plugin code

```csharp
// Initialize barcode functionality
PluginServiceHelper.PublicInitialize("http://localhost:5000", "COM3", false);

// Start listening on COM port
PluginServiceHelper.StartListening("COM3");

// Scan with selected scanner
string result = PluginServiceHelper.ScanWithSelectedScanner("Scanner Name");

// Get available COM ports
string[] ports = PluginServiceHelper.GetAvailableCOMPorts();

// Get available scanners
List<string> scanners = PluginServiceHelper.GetAvailableScanners();

// Check if port is open
bool isOpen = PluginServiceHelper.IsPortOpen;

// Show notification
PluginServiceHelper.ShowNotification("Title", "Message");
```

## Usage in External Applications

### 1. Copy ReflectionHelper to your external application

Copy the `ReflectionHelper.cs` file to your external application project.

### 2. Use in your external application

```csharp
// Initialize the helper
if (ReflectionHelper.Initialize())
{
    // Initialize barcode functionality
    ReflectionHelper.PublicInitialize("http://localhost:5000", "COM3", true);
    
    // Capture image
    ReflectionHelper.CaptureImage("COM3");
    
    // Scan with scanner
    string result = ReflectionHelper.ScanWithSelectedScanner("Scanner Name");
}
```

## Available Methods

### SerialPortManager Methods Accessible via Reflection

- **PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)** - Initialize barcode functionality
- **StartListening(string comPort)** - Start listening on COM port
- **ScanWithSelectedScanner(string scannerName)** - Scan with selected scanner
- **GetAvailableCOMPorts()** - Get available COM ports
- **GetAvailableScanners()** - Get available scanners
- **IsPortOpen** - Check if port is open
- **CaptureImageAsync(string comPort)** - Capture image from barcode scanner

### Notification Methods

- **ShowNotification(string title, string text)** - Show notification via main application's notification system

## Error Handling

The reflection system includes comprehensive error handling:

1. **Initialization Errors** - If the main assembly or types cannot be found
2. **Method Not Found** - If the requested method doesn't exist
3. **Invocation Errors** - If method calls fail
4. **Fallback Mechanisms** - Plugins can implement fallback logic when reflection fails

## Testing

Use the `ReflectionTest` class to verify the system works:

```csharp
// Test all reflection components
ReflectionTest.RunAllTests();

// Test individual components
ReflectionTest.TestSerialPortBridge();
ReflectionTest.TestPluginReflectionHelper();
ReflectionTest.TestPluginServiceAccess();
```

## Benefits

1. **No Assembly References** - Plugins don't need references to main application assemblies
2. **Loose Coupling** - Plugins are independent of main application changes
3. **Dynamic Loading** - Plugins can be loaded/unloaded without affecting main application
4. **Professional Design** - Clean separation between plugin and main application concerns
5. **Centralized Access** - All SerialPortManager functionality accessible through single interface

## Troubleshooting

### Common Issues

1. **"Main assembly not found"** - Ensure the main application (Mis.Agent) is running
2. **"Method not found"** - Verify the method exists in SerialPortManager
3. **"Initialization failed"** - Check that PluginReflectionHelper is properly initialized

### Debug Tips

- Enable console output to see reflection system messages
- Use try-catch blocks around reflection calls
- Implement fallback mechanisms for critical functionality
- Test reflection system during application startup

## Example Plugin Implementation

```csharp
[Plugin("Barcode Plugin", Version = "1.0.0")]
public class BarcodeService
{
    [PluginInitialize]
    public async Task Initialize()
    {
        try
        {
            string comPort = GetCOMPort();
            string barcodeUrl = GetBarcodeBaseUrl();
            
            // Use reflection to initialize
            PluginServiceHelper.PublicInitialize(barcodeUrl, comPort, false);
            PluginServiceHelper.ShowNotification("Barcode", "Initialized Successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing barcode service: {ex.Message}");
        }
    }
    
    [PluginNotificationHandler]
    public void ShowNotification(string title, string text)
    {
        // Use main application's notification system
        PluginServiceHelper.ShowNotification(title, text);
    }
}
```

This reflection system provides a robust, professional solution for plugin-based access to SerialPortManager functionality without assembly dependencies.
