﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Mis.Agent.Localization {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Messages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Messages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Mis.Agent.Localization.Messages", typeof(Messages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Form.
        /// </summary>
        public static string BarcodeForm {
            get {
                return ResourceManager.GetString("BarcodeForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Tab.
        /// </summary>
        public static string BarcodeTab_Text {
            get {
                return ResourceManager.GetString("BarcodeTab.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save All Settings.
        /// </summary>
        public static string BtnSaveAllSettings_Text {
            get {
                return ResourceManager.GetString("BtnSaveAllSettings.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch Language.
        /// </summary>
        public static string BtnSwitchLanguage_Text {
            get {
                return ResourceManager.GetString("BtnSwitchLanguage.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent Form .
        /// </summary>
        public static string FormTitle {
            get {
                return ResourceManager.GetString("FormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        public static string PrintTab {
            get {
                return ResourceManager.GetString("PrintTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner.
        /// </summary>
        public static string TabScanner {
            get {
                return ResourceManager.GetString("TabScanner", resourceCulture);
            }
        }
    }
}
