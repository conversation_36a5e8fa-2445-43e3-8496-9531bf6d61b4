﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;


namespace Mis.Agent.Barcode
{
    public partial class ScannerForm : Form
    {
        private readonly IScannerAppService _scannerAppService;
        public ScannerForm(IScannerAppService scannerService)
        {
            _scannerAppService = scannerService;
            InitializeComponent();
            InitializeLocalization();
            LoadScannerConfigurations();
            //_scannerAppService.scannerImageCaptured += OnScannerImageCaptured; // Subscribe to the event
        }

        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object? sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("ScannerFormTitle", "Scanner Configuration");

            // Update tab text - ensure it's never null, keep original if localization fails
            var originalText = ScannerTab.Text; // Keep the designer value as fallback
            var tabText = SimpleLocalization.GetString("ScannerTabText", "Scanner Settings");
            ScannerTab.Text = string.IsNullOrEmpty(tabText) ? (string.IsNullOrEmpty(originalText) ? "Scanner Settings" : originalText) : tabText;

            // Update labels
            label7.Text = SimpleLocalization.GetString("AvailableScannersLabel", "Available Scanners:");

            // Update checkboxes
            checkBoxUseBarcodeReader.Text = SimpleLocalization.GetString("UseBarcodeReaderCheckbox", "Use Barcode Reader");


        }




        private void LoadScannerConfigurations()
        {
            // Retrieve the value of IsScanByBarcodeReader from the configuration (appsettings.json)
            bool isScanByBarcodeReader = bool.Parse(_scannerAppService.GetSetting("IsScanByBarcodeReader"));

            // Set checkbox state based on configuration
            checkBoxUseBarcodeReader.Checked = isScanByBarcodeReader;

            // Populate available scanners
            List<string> availableScanners = _scannerAppService.GetAvailableScanners();

            if (availableScanners != null && availableScanners.Count > 0)
            {
                comboBoxScanners.DataSource = availableScanners;

                // Retrieve the selected scanner from appsettings
                string selectedScanner = _scannerAppService.GetSetting("Scanner");

                // Set the selected scanner in the ComboBox if it exists in the available scanners
                if (availableScanners.Contains(selectedScanner))
                {
                    comboBoxScanners.SelectedItem = selectedScanner;
                }
            }
            else
            {
                _scannerAppService.ShowNotification("Warning", "No available scanners found.");
            }

            // Enable or disable the ComboBox based on the value of isScanByBarcodeReader
            comboBoxScanners.Enabled = !isScanByBarcodeReader;
            pictureScanned.SizeMode = PictureBoxSizeMode.StretchImage; // To stretch the image
            //pictureScanned.Dock = DockStyle.Fill;
        }



        private void checkBoxUseBarcodeReader_CheckedChanged(object sender, EventArgs e)
        {
            comboBoxScanners.Enabled = !checkBoxUseBarcodeReader.Checked;

        }

    }
}
