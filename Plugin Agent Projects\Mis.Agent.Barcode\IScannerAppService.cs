﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    public interface IScannerAppService
    {
        string GetSetting(string key);
        List<string> GetAvailableScanners();
        void SaveScannerConfiguration(string scannerName, bool isScanByBarcodeReader);
        void ShowNotification(string title, string text);
        string GetCOMPort();
        string GetBarcodeBaseUrl();
    }
}
