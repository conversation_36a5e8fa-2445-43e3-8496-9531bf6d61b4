using System;
using System.Collections.Generic;
using System.Globalization;

namespace Mis.Agent.Localization
{
    public static class SimpleLocalization
    {
        private static CultureInfo _currentCulture = CultureInfo.CurrentCulture;
        private static readonly Dictionary<string, string> _englishStrings = new();
        private static readonly Dictionary<string, string> _arabicStrings = new();

        public static event EventHandler<CultureChangedEventArgs>? CultureChanged;

        static SimpleLocalization()
        {
            InitializeStrings();
        }

        public static CultureInfo CurrentCulture => _currentCulture;

        public static void SetCulture(string cultureName)
        {
            var culture = new CultureInfo(cultureName);
            _currentCulture = culture;
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            var isRtl = culture.TextInfo.IsRightToLeft;
            CultureChanged?.Invoke(null, new CultureChangedEventArgs
            {
                OldCulture = _currentCulture,
                NewCulture = culture,
                IsRightToLeft = isRtl
            });
        }

        public static string GetString(string key, string defaultValue = "")
        {
            var strings = _currentCulture.Name == "ar-SA" ? _arabicStrings : _englishStrings;
            return strings.TryGetValue(key, out var value) ? value : defaultValue;
        }

        private static void InitializeStrings()
        {
            // English strings
            _englishStrings["FormTitle"] = "MIS Agent";
            _englishStrings["BtnSaveAllSettings.Text"] = "Save All Settings";
            _englishStrings["SwitchToArabic"] = "Switch to Arabic";
            _englishStrings["SwitchToEnglish"] = "Switch to English";
            _englishStrings["TabBarcode"] = "Barcode";
            _englishStrings["TabPrint"] = "Print";
            _englishStrings["TabNotifications"] = "Notifications";
            _englishStrings["TabPort"] = "Port";
            _englishStrings["TabScanner"] = "Scanner";

            // Arabic strings
            _arabicStrings["FormTitle"] = "وكيل نظام المعلومات الإدارية";
            _arabicStrings["BtnSaveAllSettings.Text"] = "حفظ جميع الإعدادات";
            _arabicStrings["SwitchToArabic"] = "التبديل إلى العربية";
            _arabicStrings["SwitchToEnglish"] = "Switch to English";
            _arabicStrings["TabBarcode"] = "الباركود";
            _arabicStrings["TabPrint"] = "الطباعة";
            _arabicStrings["TabNotifications"] = "الإشعارات";
            _arabicStrings["TabPort"] = "المنفذ";
            _arabicStrings["TabScanner"] = "الماسح";
        }
    }
}
