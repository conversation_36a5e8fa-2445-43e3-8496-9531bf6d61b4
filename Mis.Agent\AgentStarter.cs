using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System.Windows.Forms;
using Mis.Agent;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Hosting;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using Mis.Agent.PluginSystem;
using Microsoft.Extensions.Configuration;
using System.Configuration;
using Mis.Agent.Models;
using Mis.Agent.Services;
using System.IO.Ports;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Mis.Agent.ApplicationsContext
{
    public class NotificationEventArgs : EventArgs
    {
        public bool IsEnabled { get; set; }
    }

    public static class NotificationManager
    {
        public static event EventHandler<NotificationEventArgs>? NotificationEvent;

        public static void Notify(NotificationEventArgs e)
        {
            NotificationEvent?.Invoke(null, e);
        }
    }

    public class AgentStarter : ApplicationContext
    {
        private IHost? _webHost;
        private NotifyIcon? trayIcon;
        private TransactionDto? _transactionDto;
        private AgentForm? _agentForm;
        IServiceProvider _serviceProvider;
        bool _notificationsEnabled = true;
        private DataCleanupService? _dataCleanupService;
        public AgentStarter(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            SerialPortManager.Instance.BarcodeImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.scannerImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.NotificationUpdated += OnNotificationUpdated;
            NotificationManager.NotificationEvent += OnNotificationReceived;

            // Initialize data cleanup service
            _dataCleanupService = new DataCleanupService();

            // Initialize plugins once on startup
            Task.Run(async () => await InitializePluginsOnStartup());

            StartSignalRServer();
            InitializeTrayIcon();
        }
        private async void StartSignalRServer()
        {
            try
            {
                // Start the SignalR server
                _webHost = Host.CreateDefaultBuilder()
                    .ConfigureWebHostDefaults(webBuilder =>
                    {
                        webBuilder.UseStartup<Startup>();
                    })
                    .Build();

                _webHost.Start();
                await ExecutePlugin();
            }
            catch (HttpRequestException ex)
            {
                // Handle exceptions related to HTTP requests
                MessageBox.Show($"Failed to connect to SignalR server: {ex.Message}", "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                // Handle any other exceptions
                MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// Initialize plugins once on application startup
        /// </summary>
        private async Task InitializePluginsOnStartup()
        {
            try
            {
                string pluginPath = Path.Combine(Application.StartupPath, "Plugins");
                var services = new ServiceCollection();

                if (!Directory.Exists(pluginPath))
                {
                    Directory.CreateDirectory(pluginPath);
                    Console.WriteLine("Plugins folder not found. Created for you.");
                }

                // Initialize the plugin system
                var pluginManager = PluginManager.Instance;
                pluginManager.SetServiceProvider(services.BuildServiceProvider());

                // Subscribe to plugin events once
                pluginManager.PluginLoaded += OnPluginLoadedStartup;
                pluginManager.PluginLoadFailed += OnPluginLoadFailedStartup;

                // Discover and load plugins
                pluginManager.DiscoverPlugins(pluginPath);
                pluginManager.LoadAutoLoadPlugins();

                Console.WriteLine($"Loaded {pluginManager.LoadedPlugins.Count} plugins successfully on startup.");

                // Initialize barcode service to start listening
                await InitializeBarcodeService();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing plugins on startup: {ex.Message}");
            }
        }

        /// <summary>
        /// Initialize barcode service to start listening for barcode data
        /// </summary>
        private async Task InitializeBarcodeService()
        {
            try
            {
                var pluginManager = PluginManager.Instance;
                var barcodePlugin = pluginManager.LoadedPlugins.FirstOrDefault(p => p.Name.Contains("Barcode"));

                if (barcodePlugin != null)
                {
                    // Initialize barcode service to start listening
                    var initMethod = barcodePlugin.GetType().GetMethod("Initialize");
                    if (initMethod != null)
                    {
                        initMethod.Invoke(barcodePlugin, null);
                        Console.WriteLine("Barcode service initialized and listening.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing barcode service: {ex.Message}");
            }
        }

        private void OnPluginLoadedStartup(object? sender, PluginLoadedEventArgs e)
        {
            Console.WriteLine($"Plugin loaded on startup: {e.Plugin.Name} v{e.Plugin.Version}");
        }

        private void OnPluginLoadFailedStartup(object? sender, PluginLoadFailedEventArgs e)
        {
            Console.WriteLine($"Plugin failed to load on startup: {e.Exception.Message}");
        }

        private async Task ExecutePlugin()
        {
            // This method is now deprecated - plugins are initialized on startup
            await Task.CompletedTask;
        }

        private void OnNotificationReceived(object? sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        private void InitializeTrayIcon()
        {
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.AddRange(new ToolStripItem[] {
            new ToolStripMenuItem("Show Tabs Form", null, ShowTabsForm),
            new ToolStripMenuItem("Exit", null, Exit)
        });

            trayIcon = new NotifyIcon
            {
                Icon = Icon.ExtractAssociatedIcon(Application.ExecutablePath),
                ContextMenuStrip = contextMenu,
                Visible = true,
                Text = "Agent Application"
            };

            trayIcon.MouseClick += TrayIcon_MouseClick;
        }



        private async void TrayIcon_MouseClick(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                await ShowOrRefreshTabsFormAsync();
            }
        }

        private async void ShowTabsForm(object? sender, EventArgs e)
        {
            try
            {
                bool result = await ShowOrRefreshTabsFormAsync();
                ShowNotification("Agent Form", result ? "TabsForm is already opened." : "TabsForm operation failed.");
            }
            catch (Exception ex)
            {
                ShowNotification("Error", $"An error occurred in ShowTabsForm: {ex.Message}");
            }
        }

        public async Task<bool> ShowOrRefreshTabsFormAsync()
        {
            try
            {
                if (_agentForm == null || _agentForm.IsDisposed)
                {
                    // Create new form only if it doesn't exist or was disposed
                    return await RunTabsFormAsync(_transactionDto);
                }
                else
                {
                    // Show the existing form if it's hidden
                    if (_agentForm.InvokeRequired)
                    {
                        _agentForm.Invoke(new Action(() =>
                        {
                            ShowExistingForm();
                        }));
                    }
                    else
                    {
                        ShowExistingForm();
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                ShowNotification("Error", $"An error occurred in ShowOrRefreshTabsFormAsync: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Shows the existing form properly
        /// </summary>
        private void ShowExistingForm()
        {
            try
            {
                if (_agentForm.WindowState == FormWindowState.Minimized)
                {
                    _agentForm.WindowState = FormWindowState.Normal;
                }

                if (!_agentForm.Visible)
                {
                    _agentForm.Show();
                }

                _agentForm.BringToFront();
                _agentForm.Activate();
                _agentForm.Focus();

                Console.WriteLine("Existing form shown successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error showing existing form: {ex.Message}");
            }
        }

        private async Task<bool> RunTabsFormAsync(TransactionDto? transactionDto)
        {
            try
            {
                var taskCompletionSource = new TaskCompletionSource<bool>();

                var transactionFormThread = new Thread(() =>
                {
                    try
                    {
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);

                        // Create form with service provider
                        _agentForm = new AgentForm(_serviceProvider)
                        {
                            StartPosition = FormStartPosition.CenterScreen
                        };

                        Console.WriteLine("New AgentForm created successfully.");
                        Application.Run(_agentForm);
                        taskCompletionSource.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error in RunTabsFormAsync thread: {ex.Message}");
                        taskCompletionSource.SetException(ex);
                    }
                });

                transactionFormThread.SetApartmentState(ApartmentState.STA);
                transactionFormThread.Start();

                return await taskCompletionSource.Task;
            }
            catch (Exception ex)
            {
                ShowNotification("Error", $"An error occurred in RunTabsFormAsync: {ex.Message}");
                return false;
            }
        }


        private void Exit(object? sender, EventArgs e)
        {
            try
            {
                // Dispose cleanup service
                _dataCleanupService?.Dispose();

                trayIcon.Visible = false;
                Application.Exit();
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in Exit method: {ex.Message}");
            }
        }
        private void OnImageCaptured(Image capturedImage)
        {
            try
            {
                // Access the ScannerTab by its name
                var scannerTab = _agentForm?.tabControl1.TabPages["ScannerTab"] as TabPage;
                if (scannerTab != null)
                {
                    var pictureBox = scannerTab.Controls["pictureScanned"] as PictureBox;
                    if (pictureBox != null)
                    {
                        pictureBox.Image = capturedImage;
                    }
                    else
                    {
                        ShowNotification("Error", "PictureBox not found.");
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.");
                }
            }
            catch (Exception ex)
            {
                ShowNotification("Error", $"Error occurred: {ex.Message}");
            }
        }
        private void OnNotificationUpdated()
        {
            RefreshDataGridView();
        }
        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                //var notifications = _publicNotificationAppService.GetAllNotificationsAsync().Result; // Implement this method
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = _agentForm?.tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}