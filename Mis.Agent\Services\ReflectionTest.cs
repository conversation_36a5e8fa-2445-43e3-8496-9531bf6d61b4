using System;
using System.Collections.Generic;

namespace Mis.Agent.Services
{
    /// <summary>
    /// Test class to verify that the reflection-based plugin system works correctly
    /// This can be used to test SerialPortManager access without assembly references
    /// </summary>
    public static class ReflectionTest
    {
        /// <summary>
        /// Test the SerialPortBridge functionality
        /// </summary>
        public static void TestSerialPortBridge()
        {
            Console.WriteLine("=== Testing SerialPortBridge ===");
            
            try
            {
                // Test getting available COM ports
                Console.WriteLine("Testing GetAvailableCOMPorts...");
                var comPorts = SerialPortBridge.GetAvailableCOMPorts();
                Console.WriteLine($"Found {comPorts.Length} COM ports: {string.Join(", ", comPorts)}");
                
                // Test getting available scanners
                Console.WriteLine("Testing GetAvailableScanners...");
                var scanners = SerialPortBridge.GetAvailableScanners();
                Console.WriteLine($"Found {scanners.Count} scanners: {string.Join(", ", scanners)}");
                
                // Test IsPortOpen property
                Console.WriteLine("Testing IsPortOpen property...");
                var isOpen = SerialPortBridge.IsPortOpen;
                Console.WriteLine($"Port is open: {isOpen}");
                
                Console.WriteLine("SerialPortBridge tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing SerialPortBridge: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test the PluginReflectionHelper functionality
        /// </summary>
        public static void TestPluginReflectionHelper()
        {
            Console.WriteLine("=== Testing PluginReflectionHelper ===");
            
            try
            {
                // Initialize the helper
                var mainAssembly = System.Reflection.Assembly.GetExecutingAssembly();
                PluginReflectionHelper.Initialize(mainAssembly);
                
                // Test getting available COM ports
                Console.WriteLine("Testing CallGetAvailableCOMPorts...");
                var comPorts = PluginReflectionHelper.CallGetAvailableCOMPorts();
                Console.WriteLine($"Found {comPorts.Length} COM ports: {string.Join(", ", comPorts)}");
                
                // Test getting available scanners
                Console.WriteLine("Testing CallGetAvailableScanners...");
                var scanners = PluginReflectionHelper.CallGetAvailableScanners();
                Console.WriteLine($"Found {scanners.Count} scanners: {string.Join(", ", scanners)}");
                
                // Test IsPortOpen property
                Console.WriteLine("Testing GetIsPortOpen...");
                var isOpen = PluginReflectionHelper.GetIsPortOpen();
                Console.WriteLine($"Port is open: {isOpen}");
                
                // Test notification
                Console.WriteLine("Testing ShowNotification...");
                PluginReflectionHelper.ShowNotification("Test", "Reflection test notification");
                
                Console.WriteLine("PluginReflectionHelper tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing PluginReflectionHelper: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test the PluginServiceAccess functionality
        /// </summary>
        public static void TestPluginServiceAccess()
        {
            Console.WriteLine("=== Testing PluginServiceAccess ===");
            
            try
            {
                // Test getting available COM ports
                Console.WriteLine("Testing GetAvailableCOMPorts...");
                var comPorts = Mis.Agent.PluginSystem.PluginManager.PluginServiceAccess.GetAvailableCOMPorts();
                Console.WriteLine($"Found {comPorts.Length} COM ports: {string.Join(", ", comPorts)}");
                
                // Test getting available scanners
                Console.WriteLine("Testing GetAvailableScanners...");
                var scanners = Mis.Agent.PluginSystem.PluginManager.PluginServiceAccess.GetAvailableScanners();
                Console.WriteLine($"Found {scanners.Count} scanners: {string.Join(", ", scanners)}");
                
                // Test IsPortOpen property
                Console.WriteLine("Testing IsPortOpen...");
                var isOpen = Mis.Agent.PluginSystem.PluginManager.PluginServiceAccess.IsPortOpen;
                Console.WriteLine($"Port is open: {isOpen}");
                
                // Test notification
                Console.WriteLine("Testing ShowNotification...");
                Mis.Agent.PluginSystem.PluginManager.PluginServiceAccess.ShowNotification("Test", "PluginServiceAccess test notification");
                
                Console.WriteLine("PluginServiceAccess tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing PluginServiceAccess: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Run all reflection tests
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("Starting reflection system tests...\n");
            
            TestSerialPortBridge();
            Console.WriteLine();
            
            TestPluginReflectionHelper();
            Console.WriteLine();
            
            TestPluginServiceAccess();
            Console.WriteLine();
            
            Console.WriteLine("All reflection tests completed!");
        }
    }
}
