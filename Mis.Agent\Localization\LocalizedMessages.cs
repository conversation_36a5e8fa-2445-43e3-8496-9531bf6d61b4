﻿using System;
using System.Globalization;
using System.Resources;
using System.Threading;

namespace Mis.Agent.Localization
{
    public static class LocalizedMessages
    {
        // اسم ملف الموارد مع الـ namespace الصحيح
        private static ResourceManager _resourceManager = new ResourceManager("Mis.Agent.Localization.Messages", typeof(LocalizedMessages).Assembly);
        private static CultureInfo _currentCulture = CultureInfo.CurrentUICulture;

        public static void SetCulture(string cultureName)
        {
            _currentCulture = new CultureInfo(cultureName);
            Thread.CurrentThread.CurrentUICulture = _currentCulture;
            Thread.CurrentThread.CurrentCulture = _currentCulture;
        }

        public static string GetMessage(string key)
        {
            string message = _resourceManager.GetString(key, _currentCulture);
            return message ?? key;
        }
    }
}
